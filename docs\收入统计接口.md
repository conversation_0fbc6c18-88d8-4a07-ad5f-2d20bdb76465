# 收入统计接口

## 概述
收入统计接口提供全面的收入数据分析功能，包括收入概览、趋势分析、服务维度统计和员工维度统计。

## 接口列表

### 1. 获取收入概览统计

**接口地址：** `GET /revenue-statistics/overview`

**请求参数：**
- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD

**响应数据：**
```json
{
  "totalRevenue": 15000.00,           // 有效收入总额（已完成+已评价）
  "totalOriginalPrice": 18000.00,     // 有效订单原价总额
  "totalPaidAmount": 16000.00,        // 总实付金额（所有状态）
  "totalRefundedAmount": 1000.00,     // 总退款金额
  "totalDiscount": 3000.00,           // 总优惠金额
  "netRevenue": 14000.00,             // 净收入（有效收入-退款）
  "discountRate": "16.67",            // 优惠率（%）
  "mainOrder": {                      // 主订单数据
    "orderCount": 50,                 // 主订单总数（所有状态）
    "effectiveRevenue": 12000.00,     // 主订单有效收入
    "paidAmount": 13000.00,           // 主订单实付金额
    "refundedAmount": 800.00,         // 主订单退款金额
    "totalOriginalPrice": 15000.00,   // 主订单原价总额
    "effectiveOriginalPrice": 14000.00, // 有效主订单原价
    "totalDiscount": 2000.00,         // 主订单优惠金额
    "avgOrderValue": "240.00"         // 主订单平均价值
  },
  "additionalService": {              // 追加服务数据
    "orderCount": 20,                 // 追加服务订单总数
    "effectiveRevenue": 3000.00,      // 追加服务有效收入
    "paidAmount": 3200.00,            // 追加服务实付金额
    "refundedAmount": 200.00,         // 追加服务退款金额
    "totalOriginalPrice": 4000.00,    // 追加服务原价总额
    "effectiveOriginalPrice": 3800.00, // 有效追加服务原价
    "totalDiscount": 1000.00,         // 追加服务优惠金额
    "avgOrderValue": "150.00"         // 追加服务平均价值
  },
  "totalOrderCount": 70,              // 总订单数（所有状态）
  "effectiveOrderCount": 65,          // 有效订单数（已完成+已评价）
  "refundedOrderCount": 5,            // 退款订单数（退款中+已退款）
  "avgOrderValue": "214.29"           // 平均订单价值
}
```

### 2. 获取收入趋势统计

**接口地址：** `GET /revenue-statistics/trend`

**请求参数：**
- `startDate` (必填): 开始日期，格式：YYYY-MM-DD
- `endDate` (必填): 结束日期，格式：YYYY-MM-DD
- `periodType` (可选): 统计周期，可选值：day/week/month，默认：day

**响应数据：**
```json
[
  {
    "period": "2024-01-01",           // 时间周期
    "mainOrder": {                    // 主订单数据
      "orderCount": 10,
      "totalRevenue": 2400.00,
      "totalOriginalPrice": 2800.00,
      "totalDiscount": 400.00
    },
    "additionalService": {            // 追加服务数据
      "orderCount": 5,
      "totalRevenue": 750.00,
      "totalOriginalPrice": 1000.00,
      "totalDiscount": 250.00
    },
    "totalRevenue": 3150.00,          // 当期总收入
    "totalOriginalPrice": 3800.00,    // 当期总原价
    "totalDiscount": 650.00,          // 当期总优惠
    "totalOrderCount": 15             // 当期总订单数
  }
]
```

### 3. 获取服务收入统计

**接口地址：** `GET /revenue-statistics/service`

**请求参数：**
- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `serviceTypeId` (可选): 服务类型ID
- `serviceId` (可选): 服务ID
- `page` (可选): 页码，默认：1
- `pageSize` (可选): 每页数量，默认：20
- `sortBy` (可选): 排序字段，可选值：totalRevenue/orderCount/avgRevenue，默认：totalRevenue
- `sortOrder` (可选): 排序方式，可选值：asc/desc，默认：desc

**响应数据：**
```json
{
  "list": [
    {
      "serviceId": 1,
      "serviceName": "幼犬洗护",
      "serviceType": "洗护服务",
      "serviceTypeId": 1,
      "totalOrderCount": 25,           // 总订单数（所有状态）
      "effectiveOrderCount": 22,       // 有效订单数（已完成+已评价）
      "refundedOrderCount": 3,         // 退款订单数（退款中+已退款）
      "totalOriginalPrice": 6000.00,   // 总原价
      "effectiveRevenue": 5280.00,     // 有效收入（已完成+已评价）
      "refundedAmount": 720.00,        // 退款金额
      "netRevenue": 4560.00,           // 净收入（有效收入-退款）
      "avgRevenue": 240.00,            // 平均收入（基于有效订单）
      "basePrice": 200.00              // 服务基础价格
    }
  ],
  "total": 10,
  "page": 1,
  "pageSize": 20
}
```

### 4. 获取员工收入统计

**接口地址：** `GET /revenue-statistics/employee`

**请求参数：**
- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `employeeId` (可选): 员工ID
- `page` (可选): 页码，默认：1
- `pageSize` (可选): 每页数量，默认：20
- `sortBy` (可选): 排序字段，可选值：totalRevenue/orderCount/avgRevenue，默认：totalRevenue
- `sortOrder` (可选): 排序方式，可选值：asc/desc，默认：desc

**响应数据：**
```json
{
  "list": [
    {
      "employeeId": 1,
      "employee": {
        "id": 1,
        "name": "张师傅",
        "phone": "13800138000",
        "avatar": "avatar.jpg",
        "rating": 4.8
      },
      "mainOrder": {
        "orderCount": 20,                // 主订单总数
        "effectiveRevenue": 4800.00,     // 主订单有效收入
        "paidAmount": 5200.00,           // 主订单实付金额
        "refundedAmount": 400.00,        // 主订单退款金额
        "totalOriginalPrice": 5600.00,   // 主订单原价总额
        "effectiveOriginalPrice": 5200.00, // 有效主订单原价
        "totalDiscount": 800.00,         // 主订单优惠金额
        "avgRevenue": 240.00             // 主订单平均收入
      },
      "additionalService": {
        "orderCount": 8,                 // 追加服务订单总数
        "effectiveRevenue": 1200.00,     // 追加服务有效收入
        "paidAmount": 1400.00,           // 追加服务实付金额
        "refundedAmount": 200.00,        // 追加服务退款金额
        "totalOriginalPrice": 1600.00,   // 追加服务原价总额
        "effectiveOriginalPrice": 1400.00, // 有效追加服务原价
        "totalDiscount": 400.00          // 追加服务优惠金额
      },
      "totalRevenue": 6000.00,           // 员工总有效收入
      "totalOrderCount": 28,             // 员工总订单数
      "avgRevenue": "214.29"             // 员工平均收入
    }
  ],
  "total": 5,
  "page": 1,
  "pageSize": 20
}
```

### 5. 获取收入构成分析

**接口地址：** `GET /revenue-statistics/composition`

**请求参数：**
- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD

**响应数据：**
```json
{
  "revenueComposition": {             // 收入构成分析
    "mainOrderRevenue": 12000.00,     // 主订单有效收入
    "mainOrderPercentage": "80.00",   // 主订单收入占比
    "additionalServiceRevenue": 3000.00, // 追加服务有效收入
    "additionalServicePercentage": "20.00" // 追加服务收入占比
  },
  "discountComposition": {            // 优惠构成分析
    "totalDiscount": 3000.00,         // 总优惠金额
    "discountRate": "16.67",          // 总优惠率
    "mainOrderDiscount": 2000.00,     // 主订单优惠金额
    "additionalServiceDiscount": 1000.00 // 追加服务优惠金额
  },
  "orderComposition": {               // 订单数量构成
    "totalOrderCount": 70,            // 总订单数（所有状态）
    "mainOrderCount": 50,             // 主订单数
    "mainOrderPercentage": "71.43",   // 主订单数量占比
    "additionalServiceCount": 20,     // 追加服务订单数
    "additionalServicePercentage": "28.57" // 追加服务数量占比
  }
}
```

## 收入计算规则

1. **统计范围**: 包含"已完成"、"已评价"、"退款中"、"已退款"四种状态的订单
2. **有效订单**: 状态为"已完成"和"已评价"的订单，用于计算有效收入
3. **退款订单**: 状态为"退款中"和"已退款"的订单，用于计算退款金额
4. **原价**: 订单的原始价格，未扣除任何优惠前的价格
5. **实付金额**: 用户实际支付的金额（totalFee），包含所有状态的订单
6. **有效收入**: 只计算已完成和已评价订单的收入
7. **退款金额**: 退款中和已退款订单的金额
8. **净收入**: 有效收入减去退款金额，表示最终实际收入
9. **优惠金额**: 权益卡抵扣 + 代金券抵扣
10. **优惠率**: 优惠金额占原价的百分比

## 时间范围说明

- 不传时间参数：统计全部历史数据
- 传入时间范围：统计指定时间范围内的数据
- 时间格式：YYYY-MM-DD
- 结束时间包含当天23:59:59

## 服务维度说明

- 支持按服务类型筛选
- 支持按具体服务筛选
- 统计数据包括：
  - 订单数量（总数、有效数、退款数）
  - 收入金额（原价、有效收入、退款金额、净收入）
  - 平均收入（基于有效订单计算）

## 员工维度说明

- 支持按员工筛选
- 分别统计主订单和追加服务数据
- 提供员工基本信息和评分
- 支持多维度排序
