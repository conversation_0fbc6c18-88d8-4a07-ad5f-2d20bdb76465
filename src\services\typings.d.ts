declare namespace API {
  /**
   * 统一返回信息格式
   */
  type ResType<T> = {
    errCode: number;
    data?: T;
    msg?: string;
  };

  interface IResponseDate {
    createdAt: Date;
    updatedAt: Date;
  }

  type LoginResponse = {
    token: string;
    refreshToken: string;
    user: Omit<User, 'password'>;
  };

  type Dictionarie = {
    /** 字典ID，主键 */
    id: number;
    /** 字典类型 */
    type: string;
    /** 字典编码 */
    code: string;
    /** 字典名称 */
    name: string;
    /** 别名 */
    alias?: string;
    /** 字典描述（可选） */
    description?: string;
    /** 排序 */
    sortOrder: number;
    /** 状态 */
    status: number;
  };

  type User = {
    id: number;
    username: string;
    password: string;
    nickname?: string;
    avatar?: string;
    email?: string;
    isActive?: boolean;
    roles?: Role[];
    features?: Feature[];
  };

  /** 系统功能 */
  type Feature = {
    /** 功能编号，主键 */
    code: string;
    /** 功能名称 */
    name: string;
    /** 父级功能编号（可选） */
    parentCode?: string;
    /** 功能图标（可选） */
    icon?: string;
    /** 功能路径 */
    path: string;
    /** 排序 */
    orderIndex: number;
    /** 描述（可选） */
    description?: string;
    permissions?: Permission[];
  };

  /** 仅限点 */
  type Permission = {
    id: number;
    /** 权限名称 */
    name: string;
    /** 权限描述 */
    description?: string;
    /** 关联的功能编号 */
    featureCode: string;
    feature?: Feature;
    roles?: Role[];
  };

  /** 角色 */
  type Role = {
    id: number;
    /** 角色名称 */
    name: string;
    /** 角色描述 */
    description?: string;
    users?: User[];
    permissions?: Permission[];
  };

  /** 行政区划 */
  type Area = {
    /** 行政区划编码,主键 */
    code: string;
    /** 行政区划名称 */
    name: string;
    /** 父级行政区划编码 */
    parentCode?: string;
  };

  /** 车辆 */
  type Vehicle = {
    /** 车辆ID */
    id: number;
    /** 车牌号 */
    plateNumber: string;
    /** 车辆类型 */
    vehicleType?: string;
    /** 实时纬度 */
    latitude?: number;
    /** 实时经度 */
    longitude?: number;
    /** 车辆状态（空闲/服务中） */
    status: string;
    /** 里程数（公里） */
    mileage?: number;
    /** 外观描述 */
    appearance?: string;
    /** 保险到期时间 */
    insuranceExpiry?: string;
    /** 行驶证到期时间 */
    licenseExpiry?: string;
    /** 物资清单 */
    supplies?: string;
    /** 最后提交时间（员工端提交） */
    lastSubmittedAt?: string;
    /** 最后提交人ID */
    lastSubmittedBy?: number;
    /** 最后提交员工信息 */
    lastSubmittedEmployee?: Employee;
    /** 关联的员工信息 */
    employee?: Employee;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
  };

  /** 员工 */
  type Employee = {
    /** 员工ID */
    id: number;
    /** 真实姓名 */
    name: string;
    /** 手机号 */
    phone: string;
    /** 头像 */
    avatar?: string;
    /** 职位 */
    position: string;
    /** 接单等级（1-5级） */
    level?: number;
    /** 工作经验（月） */
    workExp?: number;
    /** 服务评分（0-5分） */
    rating?: number;
    /** 钱包余额 */
    walletBalance: number;
    /** 所属车辆ID */
    vehicleId?: number;
    /** 关联的车辆信息 */
    vehicle?: Vehicle;
    /** 关联的订单列表 */
    orders?: Order[];
    /** 最后打卡时间 */
    lastCheckinTime?: Date;
    /** 资格证件列表，存储证件名称和图片链接 */
    certificates?: Array<{
      /** 证件名称 */
      name: string;
      /** 证件图片链接 */
      imageUrl: string;
    }>;
    /** 入职时间 */
    hireDate?: string;
    /** 离职时间 */
    resignDate?: string;
    /** 状态：0-离职 1-在职 */
    status: number;
  };

  /** 客户 */
  type Customer = {
    /** 客户ID */
    id: number;
    /** 手机号 */
    phone: string;
    /** 昵称 */
    nickname?: string;
    /** 头像 */
    avatar?: string;
    /** 性别：0-女 1-男 */
    gender?: number;
    /** 详细地址 */
    address?: string;
    /** 会员状态：0-非会员 1-会员 */
    memberStatus: number;
    /** 积分值 */
    points: number;
    /** 最后登录时间 */
    lastLoginTime?: Date;
    /** 关联的宠物列表 */
    pets?: Pet[];
    /** 关联的订单列表 */
    orders?: Order[];
    /** 关联的权益卡列表 */
    membershipCards?: CustomerMembershipCard[];
    /** 关联的地址列表 */
    addresses?: CustomerAddress[];
    /** 状态：0-禁用 1-启用 */
    status: number;
  };

  /** 客户地址 */
  interface CustomerAddress {
    /** 地址ID */
    id: number;
    /** 关联客户ID */
    customerId: number;
    /** 地址名称/标签 */
    name?: string;
    /** 详细地址 */
    address?: string;
    /** 地址文本（后端实际字段） */
    addressText?: string;
    /** 地址详情 */
    addressDetail?: string;
    detailAddress?: string;
    /** 地址编码 */
    addressCode?: string;
    /** 邮政编码 */
    postCode?: string;
    /** 城市名称 */
    cityName?: string;
    /** 省份名称 */
    provinceName?: string;
    /** 区县名称 */
    districtName?: string;
    /** 联系电话 */
    contactPhone?: string;
    /** 经度 */
    longitude?: number | string;
    /** 纬度 */
    latitude?: number | string;
    /** 地址备注 */
    addressRemark?: string;
    /** 备注（后端实际字段） */
    remark?: string;
    /** 是否为默认地址 */
    isDefault?: boolean;
    /** 创建时间 */
    createdAt?: Date | string;
    /** 更新时间 */
    updatedAt?: Date | string;
    /** 关联的客户信息 */
    customer?: Customer;
  }

  /** 轮播图 */
  type Banner = {
    /** 轮播图ID */
    id: number;
    /** 图片链接 */
    imageURL: string;
    /** 跳转类型 */
    jumpType?: 'custom' | 'activity' | null;
    /** 跳转链接 */
    jumpLink?: string;
    /** 展示优先级（1-10），默认5，数值越大优先级越高 */
    priority: number;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
  };

  /** 活动页 */
  type Activity = {
    /** 活动ID */
    id: number;
    /** 活动标题 */
    title: string;
    /** 内容类型（content-富文本，url-链接地址） */
    contentType: 'content' | 'url';
    /** 富文本内容 */
    content?: string;
    /** 活动链接地址 */
    url?: string;
    /** 受众（用户端/员工端） */
    target: string;
    /** 封面图片链接 */
    coverImage?: string;
    /** 发布状态（0-未发布，1-已发布） */
    isPublished: number;
    /** 发布时间 */
    publishedAt?: string;
    /** 创建时间 */
    createdAt: string;
    /** 更新时间 */
    updatedAt: string;
  };

  /** 宠物 */
  interface Pet {
    /** 宠物ID */
    id: number;
    /** 关联用户ID */
    customerId: number;
    /** 宠物名称 */
    name: string;
    /** 头像 */
    avatar?: string;
    /** 宠物类型，例如 猫、狗 */
    type: string;
    /** 品种，例如金毛 */
    breed?: string;
    /** 性别：0-未知 1-男 2-女 */
    gender: number;
    /** 出生年月 */
    birthday?: Date;
    /** 年龄（月），虚拟字段 */
    bri?: number;
    /** 毛发类型 */
    hairType: string;
    /** 体重Kg */
    weight?: number;
    /** 是否疫苗 */
    isVaccine?: boolean;
    /** 是否绝育 */
    isSterilization?: boolean;
    /** 是否驱虫 */
    isRepellent?: boolean;
    /** 排序值 */
    orderIndex: number;
    /** 关联客户信息 */
    customer?: Customer;
  }

  /** 服务品牌 */
  type ServiceType = {
    /** 类目ID */
    id: number;
    /** 服务名称 */
    name: string;
    /** 服务描述 */
    description?: string;
    /** 服务类型 */
    type: string;
    /** 排序值 */
    orderIndex: number;
    /** 关联的服务列表 */
    services?: Service[];
  };

  /** 服务项目 */
  type Service = {
    /** 服务ID */
    id: number;
    /** 服务品牌ID */
    serviceTypeId: number;
    /** 服务名称 */
    serviceName: string;
    /** 服务logo */
    logo?: string;
    /** 服务描述 */
    description?: string;
    /** 基础价格 */
    basePrice: number;
    /** 宠物类型 */
    petTypes: string;
    /** 适用体型 */
    size?: string | null;
    /** 宠物重量描述 */
    weightDescription?: string;
    /** 适用毛发类型 */
    hairType?: string | null;
    /** 是否按距离计费 */
    distanceChargeFlag: boolean;
    /** 是否支持权益卡抵扣 */
    cardDiscountFlag: boolean;
    /** 排序值 */
    orderIndex: number;
    /** 是否发布 */
    published: boolean;
    /** 平均服务时长（分钟） */
    avgDuration?: number;
    /** 关联的服务类型信息 */
    serviceType?: ServiceType;
    /** 增项服务 */
    additionalServices?: AdditionalService[];
  };

  /** 增项服务 */
  type AdditionalService = {
    /** 增项服务ID */
    id: number;
    /** 服务名称 */
    name: string;
    /** 服务图标 */
    icon?: string;
    /** 服务类型 */
    type: string;
    /** 服务价格 */
    price: number;
    /** 服务时长(分钟) */
    duration: number;
    /** 服务说明 */
    description?: string;
  };

  /** 追加服务订单状态枚举 */
  enum AdditionalServiceOrderStatus {
    PENDING_CONFIRM = 'pending_confirm', // 待确认
    CONFIRMED = 'confirmed', // 已确认
    REJECTED = 'rejected', // 已拒绝
    PENDING_PAYMENT = 'pending_payment', // 待付款
    PAID = 'paid', // 已付款/服务中
    COMPLETED = 'completed', // 已完成
    CANCELLED = 'cancelled', // 已取消
    REFUNDING = 'refunding', // 退款中
    REFUNDED = 'refunded', // 已退款
  }

  /** 追加服务订单 */
  interface AdditionalServiceOrder extends IResponseDate {
    /** 追加服务订单ID */
    id: number;
    /** 追加服务订单编号 */
    sn: string;
    /** 关联订单详情ID */
    orderDetailId: number;
    /** 状态 */
    status: AdditionalServiceOrderStatus;
    /** 原价 */
    originalPrice: number;
    /** 实付金额 */
    totalFee: number;
    /** 服务详情列表 */
    details?: AdditionalServiceOrderDetail[];
    /** 关联客户信息 */
    customer?: {
      id: number;
      nickname: string;
      phone: string;
    };
  }

  /** 追加服务订单详情 */
  interface AdditionalServiceOrderDetail {
    /** 详情ID */
    id: number;
    /** 服务ID */
    serviceId: number;
    /** 服务名称 */
    serviceName: string;
    /** 服务价格 */
    servicePrice: number;
    /** 数量 */
    quantity: number;
    /** 关联服务信息 */
    service?: {
      id: number;
      serviceName: string;
    };
  }

  /** 订单明细 */
  interface OrderDetail {
    /** 明细ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 关联服务ID */
    serviceId: number;
    /** 服务名称，确保删除服务后订单明细的服务名称不丢失 */
    serviceName: string;
    /** 服务基础价格，确保服务价格变更后订单明细的价格不受影响 */
    servicePrice: number;
    /** 关联宠物ID */
    petId?: number;
    /** 宠物名称，确保删除宠物后订单明细的宠物名称不丢失 */
    petName: string;
    /** 宠物类型，确保删除宠物后订单明细的宠物类型不丢失 */
    petType: string;
    /** 宠物品种，确保删除宠物后订单明细的宠物品种不丢失 */
    petBreed?: string;
    /** 下单时间 */
    orderTime: Date;
    /** 状态 */
    status: string;
    /** 用户备注 */
    userRemark?: string;
    /** 关联订单信息 */
    order?: Order;
    /** 关联服务信息 */
    service?: Service;
    /** 关联宠物信息 */
    pet?: Pet;
    /** 关联增项服务列表 */
    additionalServices?: AdditionalService[];
  }

  /** 评价信息 */
  interface Review {
    /** 评价ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 评分 */
    rating: number;
    /** 文字评价 */
    comment?: string;
    /** 照片链接列表 */
    photoURLs?: string[];
    /** 关联的订单信息 */
    order?: Order;
  }

  /** 投诉建议大类 */
  enum ComplaintCategory {
    投诉 = 'complaint',
    建议 = 'suggestion',
  }

  /** 投诉建议小类 */
  enum ComplaintSubCategory {
    订单投诉 = 'order',
    人员投诉 = 'employee',
    平台建议 = 'platform',
    服务建议 = 'service',
    流程投诉 = 'workflow',
  }

  /** 投诉建议处理状态 */
  enum ComplaintStatus {
    待处理 = 'pending',
    处理中 = 'processing',
    已解决 = 'resolved',
    已关闭 = 'closed',
  }

  /** 投诉建议 */
  interface Complaint extends IResponseDate {
    /** 投诉建议ID */
    id: number;
    /** 关联用户ID（可为空） */
    customerId?: number;
    /** 关联订单ID（可选） */
    orderId?: number;
    /** 关联员工ID（可选） */
    employeeId?: number;
    /** 大类：投诉/建议 */
    category: ComplaintCategory;
    /** 小类：订单/人员/平台/服务/流程 */
    subCategory: ComplaintSubCategory;
    /** 标题 */
    title: string;
    /** 内容 */
    content: string;
    /** 联系方式 */
    contactInfo?: string;
    /** 图片URL数组 */
    photoURLs?: string[];
    /** 处理状态 */
    status: ComplaintStatus;
    /** 处理结果 */
    result?: string;
    /** 处理人员ID */
    handlerId?: number;
    /** 处理时间 */
    handledAt?: Date;
    /** 录入人员ID（管理员录入时使用） */
    createdBy?: number;
    /** 管理员录入备注 */
    adminNote?: string;
    /** 关联的用户信息 */
    customer?: Customer;
    /** 关联的订单信息 */
    order?: Order;
    /** 关联的员工信息 */
    employee?: Employee;
    /** 处理人员信息 */
    handler?: User;
  }

  /** 投诉建议处理历史记录 */
  interface ComplaintHistory {
    /** 历史记录ID */
    id: number;
    /** 操作类型 */
    operationType: 'create' | 'handle' | 'update' | 'delete';
    /** 操作人类型 */
    operatorType: 'customer' | 'admin' | 'employee';
    /** 操作人信息 */
    operator: {
      type: 'customer' | 'admin' | 'employee';
      id: number;
      name: string;
      phone?: string;
      avatar?: string;
    };
    /** 操作前状态 */
    beforeStatus?: string;
    /** 操作后状态 */
    afterStatus?: string;
    /** 操作描述 */
    description: string;
    /** 操作详情 */
    operationDetails?: Record<string, any>;
    /** 操作时间 */
    operatedAt: string;
  }

  /** 投诉建议统计信息 */
  interface ComplaintStatistics {
    /** 总数 */
    total: number;
    /** 今日新增 */
    todayCount: number;
    /** 本周新增 */
    weekCount: number;
    /** 本月新增 */
    monthCount: number;
    /** 待处理数量 */
    pendingCount: number;
    /** 处理中数量 */
    processingCount: number;
    /** 已解决数量 */
    resolvedCount: number;
    /** 已关闭数量 */
    closedCount: number;
    /** 按大类统计 */
    categoryStats: {
      complaint: number;
      suggestion: number;
    };
    /** 按小类统计 */
    subCategoryStats: {
      order: number;
      employee: number;
      platform: number;
      service: number;
    };
    /** 平均处理时长（小时） */
    avgHandleTime: number;
  }

  // ==================== 投诉建议统计接口类型定义 ====================

  /** 投诉建议概览统计 */
  interface ComplaintOverviewStats {
    complaintStats: {
      total: number;
      today: number;
      week: number;
      month: number;
      processedCount: number;
      processRate: number;
    };
    statusStats: Array<{
      status: string;
      count: number;
      percentage: number;
    }>;
    categoryStats: Array<{
      category: string;
      count: number;
      percentage: number;
    }>;
    subCategoryStats: Array<{
      subCategory: string;
      count: number;
      percentage: number;
    }>;
  }

  /** 投诉建议趋势统计 */
  interface ComplaintTrendStats {
    period: string;
    totalCount: number;
    complaintCount: number;
    suggestionCount: number;
    resolvedCount: number;
    resolveRate: number;
  }

  /** 客户投诉建议统计 */
  interface ComplaintCustomerStats {
    customerId: number;
    customerName: string;
    customerPhone: string;
    customerAvatar?: string;
    memberStatus: number;
    totalCount: number;
    complaintCount: number;
    suggestionCount: number;
    resolvedCount: number;
    resolveRate: number;
  }

  /** 员工投诉统计 */
  interface ComplaintEmployeeStats {
    employeeId: number;
    employeeName: string;
    employeePhone: string;
    employeeAvatar?: string;
    employeeRating: number;
    complaintCount: number;
    resolvedCount: number;
    pendingCount: number;
    resolveRate: number;
  }

  /** 处理效率统计 */
  interface ComplaintProcessingEfficiencyStats {
    averageProcessingHours: number;
    fastProcessingCount: number;
    slowProcessingCount: number;
    fastProcessingRate: number;
    slowProcessingRate: number;
    categoryEfficiency: Array<{
      category: string;
      count: number;
      averageHours: number;
      fastCount: number;
      slowCount: number;
    }>;
    subCategoryEfficiency: Array<{
      subCategory: string;
      count: number;
      averageHours: number;
      fastCount: number;
      slowCount: number;
    }>;
  }

  /** 热点问题分析 */
  interface ComplaintHotIssuesStats {
    hotKeywords: Array<{
      keyword: string;
      count: number;
    }>;
    subCategoryHotIssues: Array<{
      subCategory: string;
      count: number;
    }>;
  }

  /** 状态分布统计 */
  interface ComplaintStatusDistributionStats {
    statusStats: Array<{
      status: string;
      count: number;
      percentage: number;
    }>;
    categoryStats: Array<{
      category: string;
      count: number;
      percentage: number;
    }>;
    subCategoryStats: Array<{
      subCategory: string;
      count: number;
      percentage: number;
    }>;
  }

  /** 投诉建议排行榜 */
  interface ComplaintRankingStats {
    type: string;
    list: Array<{
      customerId?: number;
      customerName?: string;
      employeeId?: number;
      employeeName?: string;
      totalCount: number;
      complaintCount: number;
      suggestionCount: number;
    }>;
  }

  /** 处理时效统计 */
  interface ComplaintProcessingTimeStats {
    averageProcessingHours: number;
    fastProcessingCount: number;
    slowProcessingCount: number;
    fastProcessingRate: number;
    slowProcessingRate: number;
    timeDistribution: {
      '0-24h': number;
      '24-72h': number;
      '72h+': number;
    };
  }

  /** 解决率统计 */
  interface ComplaintResolveRateStats {
    groupBy: string;
    data: Array<{
      name: string;
      count: number;
      percentage: number;
    }>;
    totalResolveRate: number;
  }

  /** 处理人员统计 */
  interface ComplaintHandlerStats {
    handlerId: number;
    handledCount: number;
    avgProcessingHours: number;
    resolvedCount: number;
    resolveRate: number;
  }

  /** 时间分布统计 */
  interface ComplaintTimeDistributionStats {
    period: number;
    label: string;
    count: number;
    complaintCount: number;
    suggestionCount: number;
  }

  /** 满意度统计 */
  interface ComplaintSatisfactionStats {
    total: number;
    satisfiedCount: number;
    unsatisfiedCount: number;
    neutralCount: number;
    satisfactionRate: number;
    unsatisfactionRate: number;
    neutralRate: number;
  }

  /** 分页响应 */
  interface PaginatedResponse<T> {
    list: T[];
    total: number;
    page: number;
    pageSize: number;
  }

  /** 订单特殊情况说明 */
  interface OrderSpecialNote extends IResponseDate {
    /** 特殊情况说明ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 员工ID */
    employeeId: number;
    /** 特殊情况说明内容 */
    content: string;
    /** 特殊情况图片URL数组 */
    photos: string[];
    /** 关联的订单信息 */
    order?: Order;
    /** 关联的员工信息 */
    employee?: Employee;
  }

  /** 订单 */
  interface Order extends IResponseDate {
    /** 订单ID */
    id: number;
    /** 订单编号 */
    sn: string;
    /** 客户ID */
    customerId: number;
    /** 员工ID */
    employeeId?: number;
    /** 订单状态 */
    status: string;
    /** 下单时间 */
    orderTime: Date;
    /** 预约服务时间 */
    serviceTime?: Date;
    /** 地址ID */
    addressId?: number;
    /** 服务地址 */
    address: string;
    /** 服务地址经度 */
    longitude: number;
    /** 服务地址纬度 */
    latitude: number;
    /** 服务地址详情 */
    addressDetail: string;
    /** 服务地址备注 */
    addressRemark?: string;
    /** 订单总费用 */
    totalFee: number;
    /** 权益卡抵扣金额 */
    cardDeduction: number;
    /** 代金券抵扣金额 */
    couponDeduction: number;
    /** 是否有追加服务 */
    hasAdditionalServices: boolean;
    /** 追加服务实付总价（所有状态的追加服务totalFee总和） */
    additionalServiceAmount: number;
    /** 追加服务原总价 */
    additionalServiceOriginalPrice: number;
    /** 追加服务是否全部完成 */
    additionalServicesCompleted: boolean;
    /** 是否有特殊情况说明 */
    hasSpecialNote: boolean;
    /** 关联客户信息 */
    customer?: Customer;
    /** 关联员工信息 */
    employee?: Employee;
    /** 关联地址信息 */
    customerAddress?: CustomerAddress;
    /** 订单明细列表 */
    orderDetails?: OrderDetail[];
    /** 服务变更记录列表 */
    changeLogs?: ServiceChangeLog[];
    /** 评价信息 */
    review?: Review;
    /** 投诉记录列表 */
    complaints?: Complaint[];
    /** 特殊情况说明 */
    specialNote?: OrderSpecialNote;
  }

  /** 服务照片 */
  interface ServicePhoto extends IResponseDate {
    /** 服务照片ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 员工ID */
    employeeId: number;
    /** 服务前照片 */
    beforePhotos: string[];
    /** 服务前拍照时间 */
    beforePhotoTime?: string;
    /** 服务后照片 */
    afterPhotos: string[];
    /** 服务后拍照时间 */
    afterPhotoTime?: string;
    /** 关联的订单信息 */
    order?: Order;
    /** 关联的员工信息 */
    employee?: Employee;
  }

  /** 照片墙 */
  interface PhotoWall extends IResponseDate {
    /** 照片ID */
    id: number;
    /** 关联订单ID */
    orderId?: number;
    /** 关联员工ID */
    employeeId?: number;
    /** 关联客户ID */
    customerId?: number;
    /** 服务前照片链接 */
    beforePhoto: string;
    /** 服务后照片链接 */
    afterPhoto: string;
    /** 照片标题 */
    title?: string;
    /** 照片描述 */
    description?: string;
    /** 服务类型名称 */
    serviceTypeName?: string;
    /** 宠物名称 */
    petName?: string;
    /** 宠物类型 */
    petType?: string;
    /** 是否启用展示 */
    isEnabled: boolean;
    /** 展示优先级 */
    priority: number;
    /** 点赞数 */
    likeCount: number;
    /** 浏览数 */
    viewCount: number;
    /** 关联的订单信息 */
    order?: Order;
    /** 关联的员工信息 */
    employee?: Employee;
    /** 关联的客户信息 */
    customer?: Customer;
  }

  /** 照片墙统计信息 */
  interface PhotoWallStatistics {
    /** 总数 */
    total: number;
    /** 启用数量 */
    enabled: number;
    /** 禁用数量 */
    disabled: number;
    /** 总浏览数 */
    totalViews: number;
    /** 总点赞数 */
    totalLikes: number;
  }

  /** 服务日志 */
  interface ServiceChangeLog extends IResponseDate {
    /** 变更记录ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 变更类型 */
    changeType: string;
    /** 用户端发起人 */
    customerId?: number;
    /** 员工端发起人 */
    employeeId?: number;
    /** 描述 */
    description?: string;
    /** 关联的订单信息 */
    order?: Order;
    /** 用户端发起人信息 */
    customer?: Customer;
    /** 员工端发起人信息 */
    employee?: Employee;
  }

  enum ApplicableScope {
    不限 = 'all',
    所有服务 = 'allServices',
    指定服务类别 = 'serviceType',
    指定服务品牌 = 'serviceCategory',
    指定服务 = 'service',
    所有商品 = 'allProducts',
    指定商品类别 = 'productCategory',
    指定商品 = 'product',
  }

  /** 权益卡类型 */
  interface MembershipCardType {
    /** 卡类型唯一标识 */
    id: number;
    /** 名称 */
    name: string;
    /** 售价 */
    price: number;
    /** 权益卡类型，折扣卡或次卡 */
    type: 'discount' | 'times';
    /** 有效期天数 */
    validDays?: number;
    /** 折扣率 */
    discountRate?: number;
    /** 可用次数 */
    usageLimit?: number;
    /** 适用范围 */
    applicableScope: ApplicableScope;
    /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
    applicableServiceTypes?: string[];
    /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
    applicableServiceCategories?: number[];
    /** 适用服务ID列表，适用范围为指定服务时必填 */
    applicableServices?: number[];
    /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
    applicableProductTypes?: number[];
    /** 适用商品ID列表，适用范围为指定商品时必填 */
    applicableProducts?: number[];
    /** 权益描述 */
    description?: string;
    /** 是否启用 */
    isEnabled: boolean;
    /** 关联的用户权益卡信息 */
    userMembershipCards?: CustomerMembershipCard[];
    /** 发放数量（虚拟字段） */
    userCount?: number;
  }

  /** 代金券 */
  interface Coupon {
    /** 代金券唯一标识 */
    id: number;
    /** 类别，从字典获取 */
    type?: string;
    /** 售价 */
    price: number;
    /** 面值 */
    amount: number;
    /** 使用门槛 */
    threshold: number;
    /** 有效天数 */
    validDays?: number;
    /** 可用次数 */
    usageLimit?: number;
    /** 适用范围 */
    applicableScope: ApplicableScope;
    /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
    applicableServiceTypes?: string[];
    /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
    applicableServiceCategories?: number[];
    /** 适用服务ID列表，适用范围为指定服务时必填 */
    applicableServices?: number[];
    /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
    applicableProductTypes?: number[];
    /** 适用商品ID列表，适用范围为指定商品时必填 */
    applicableProducts?: number[];
    /** 权益描述 */
    description?: string;
    /** 是否启用 */
    isEnabled: boolean;
    /** 关联的用户代金券信息 */
    userCoupons?: CustomerCoupon[];
  }

  /** 用户权益卡 */
  interface CustomerMembershipCard extends IResponseDate {
    /** 用户权益卡唯一标识 */
    id: number;
    /** 关联用户ID */
    customerId: number;
    /** 关联权益卡类型ID */
    cardTypeId: number;
    /** 购买时间 */
    purchaseTime: Date;
    /** 到期时间 */
    expiryTime?: Date;
    /** 剩余使用次数，-1表示不限次数 */
    remainTimes: number;
    /** 状态：active-有效，expired-已过期，used-已用完 */
    status: 'active' | 'expired' | 'used';
    /** 关联的用户信息 */
    customer?: Customer;
    /** 关联的权益卡类型信息 */
    cardType?: MembershipCardType;
  }

  // ==================== 用户统计相关类型定义 ====================

  /** 用户概览统计 */
  interface CustomerOverviewStats {
    /** 总用户数 */
    totalUsers: number;
    /** 今日新增用户数 */
    todayNewUsers: number;
    /** 本月新增用户数 */
    monthNewUsers: number;
    /** 性别分布统计 */
    genderDistribution: CustomerGenderDistribution[];
    /** 会员状态分布统计 */
    memberStatusDistribution: CustomerMemberStatusDistribution[];
  }

  /** 用户性别分布统计 */
  interface CustomerGenderDistribution {
    /** 性别：0-女，1-男，2-未知 */
    gender: number;
    /** 性别名称 */
    genderName: string;
    /** 该性别用户数量 */
    count: number;
    /** 占总用户的百分比 */
    percentage: number;
  }

  /** 用户会员状态分布统计 */
  interface CustomerMemberStatusDistribution {
    /** 会员状态：0-普通会员，1-权益会员 */
    memberStatus: number;
    /** 会员状态名称 */
    statusName: string;
    /** 该状态用户数量 */
    count: number;
    /** 占总用户的百分比 */
    percentage: number;
  }

  /** 用户注册趋势统计 */
  interface CustomerRegistrationTrend {
    /** 时间周期 */
    period: string;
    /** 该周期内注册用户数 */
    count: number;
  }

  /** 用户统计汇总 */
  interface CustomerStatisticsSummary {
    /** 概览统计 */
    overview: {
      totalUsers: number;
      todayNewUsers: number;
      monthNewUsers: number;
    };
    /** 性别分布统计 */
    genderDistribution: CustomerGenderDistribution[];
    /** 会员状态分布统计 */
    memberStatusDistribution: CustomerMemberStatusDistribution[];
  }

  /** 用户增长趋势 */
  interface CustomerGrowthTrend {
    /** 时间段描述 */
    period: string;
    /** 趋势数据 */
    data: CustomerRegistrationTrend[];
  }

  /** 用户代金券 */
  interface CustomerCoupon extends IResponseDate {
    /** 客户代金券唯一标识 */
    id: number;
    /** 关联客户 */
    customerId: number;
    /** 关联代金券 */
    couponId: number;
    /** 领取时间 */
    receiveTime: Date;
    /** 到期时间，null表示无到期时间 */
    expiryTime?: Date;
    /** 剩余使用次数，-1表示不限次数 */
    remainTimes: number;
    /** 状态：active-有效，used-已使用，expired-已过期 */
    status: 'active' | 'used' | 'expired';
    /** 最后一次使用时间 */
    lastUseTime?: Date;
    /** 关联的客户信息 */
    customer?: Customer;
    /** 关联的代金券信息 */
    coupon?: Coupon;
    /** 使用记录 */
    usageRecords?: CouponUsageRecord[];
  }

  interface CouponUsageRecord {
    /** 使用记录唯一标识 */
    id: number;
    /** 关联的客户代金券 */
    customerCouponId: number;
    /** 使用时间 */
    useTime: Date;
    /** 使用的订单 */
    orderId: number;
    /** 关联的客户代金券信息 */
    customerCoupon?: CustomerCoupon;
    /** 关联的订单信息 */
    order?: Order;
  }

  /**
   * 权益卡订单状态枚举
   */
  export enum MembershipCardOrderStatus {
    待付款 = 'pending_payment',
    已付款 = 'paid',
    已取消 = 'cancelled',
    已退款 = 'refunded',
  }

  /**
   * 权益卡订单属性
   */
  interface MembershipCardOrder {
    /** 订单ID */
    id: number;
    /** 订单编号 */
    sn: string;
    /** 关联的用户ID */
    customerId: number;
    /** 关联的权益卡类型ID */
    cardTypeId: number;
    /** 订单金额 */
    amount: number;
    /** 订单状态 */
    status: MembershipCardOrderStatus;
    /** 创建时间 */
    createdAt?: Date;
    /** 支付时间 */
    payTime?: Date;
    /** 取消时间 */
    cancelTime?: Date;
    /** 退款时间 */
    refundTime?: Date;
    /** 微信支付预支付ID */
    prepayId?: string;
    /** 备注 */
    remark?: string;
    /** 关联的用户 */
    customer?: Customer;
    /** 关联的权益卡类型 */
    cardType?: MembershipCardType;
  }

  // ==================== 服务时长统计相关类型定义 ====================

  /**
   * 服务时长记录类型
   */
  type ServiceDurationRecordType = 'main_service' | 'additional_service';

  /**
   * 服务时长记录
   */
  interface ServiceDurationRecord {
    /** 记录ID */
    id: number;
    /** 订单ID */
    orderId: number;
    /** 记录类型 */
    recordType: ServiceDurationRecordType;
    /** 服务名称 */
    serviceName: string;
    /** 开始时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
    /** 时长（分钟） */
    duration: number;
    /** 关联订单信息 */
    order?: {
      id: number;
      sn: string;
      status: string;
      customer?: {
        id: number;
        nickname: string;
        phone: string;
      };
    };
    /** 关联员工信息 */
    employee?: {
      id: number;
      name: string;
      phone?: string;
    };
    /** 关联服务信息（主服务记录） */
    service?: {
      id: number;
      serviceName: string;
      avgDuration: number;
    };
  }

  /**
   * 分页信息
   */
  interface PaginationInfo {
    /** 总记录数 */
    total: number;
    /** 当前页码 */
    page: number;
    /** 每页数量 */
    pageSize: number;
    /** 总页数 */
    totalPages: number;
  }

  /**
   * 按员工查询时长记录响应
   */
  interface EmployeeDurationRecordsResponse {
    /** 记录列表 */
    records: ServiceDurationRecord[];
    /** 分页信息 */
    pagination: PaginationInfo;
  }

  /**
   * 订单时长统计详情
   */
  interface OrderDurationStatistics {
    /** 订单ID */
    orderId: number;
    /** 总记录数 */
    totalRecords: number;
    /** 主服务记录数 */
    mainServiceRecords: number;
    /** 增项服务记录数 */
    additionalServiceRecords: number;
    /** 累计时长（分钟） */
    totalDuration: number;
    /** 主服务时长（分钟） */
    mainServiceDuration: number;
    /** 增项服务时长（分钟） */
    additionalServiceDuration: number;
    /** 服务跨度时长（分钟） */
    serviceDuration: number;
    /** 记录列表 */
    records: ServiceDurationRecord[];
  }

  /**
   * 时长统计信息
   */
  interface DurationStatistics {
    /** 总记录数 */
    totalRecords: number;
    /** 总时长（分钟） */
    totalDuration: number;
    /** 平均时长（分钟） */
    avgDuration: number;
    /** 最短时长（分钟） */
    minDuration: number;
    /** 最长时长（分钟） */
    maxDuration: number;
  }

  /**
   * 主服务时长统计响应
   */
  interface ServiceDurationStatisticsResponse {
    /** 服务ID */
    serviceId: number;
    /** 服务名称 */
    serviceName: string;
    /** 系统记录的平均时长 */
    systemAvgDuration: number;
    /** 统计信息 */
    statistics: DurationStatistics;
    /** 记录列表 */
    records: ServiceDurationRecord[];
    /** 分页信息 */
    pagination: PaginationInfo;
  }

  /**
   * 增项服务时长统计响应
   */
  interface AdditionalServiceDurationStatisticsResponse {
    /** 增项服务ID */
    additionalServiceId: number;
    /** 增项服务名称 */
    additionalServiceName: string;
    /** 系统记录的平均时长 */
    systemAvgDuration: number;
    /** 统计信息 */
    statistics: DurationStatistics;
    /** 记录列表 */
    records: ServiceDurationRecord[];
    /** 分页信息 */
    pagination: PaginationInfo;
  }

  /** 推广记录表 */
  interface PromotionRecord {
    /** 记录ID */
    id: number;
    /** 分享人微信openid */
    sharerOpenid: string;
    /** 分享人系统用户ID */
    sharerUserId: number;
    /** 分享时间 */
    shareTime: Date;
    /** 注册人微信openid */
    registrantOpenid: string;
    /** 注册人系统用户ID */
    registrantUserId?: number;
    /** 注册时间 */
    registerTime?: Date;
    /** 分享码 */
    promotionCode: string;
    /** 状态：有效、已使用、过期 */
    status: string;
    sharer: Customer;
    registrant: Customer;
  }

  /** 员工推广关系 */
  interface EmployeePromotion {
    /** 用户ID */
    id: number;
    /** 用户昵称 */
    nickname: string;
    /** 用户手机号 */
    phone: string;
    /** 会员状态：0-非会员 1-会员 */
    memberStatus: number;
    /** 推广员工ID */
    promotionEmployeeId: number;
    /** 更新时间 */
    updatedAt: string;
    /** 推广员工信息 */
    promotionEmployee: {
      /** 员工ID */
      id: number;
      /** 员工姓名 */
      name: string;
      /** 员工手机号 */
      phone: string;
      /** 推广码 */
      promotionCode: string;
    };
  }

  // ==================== 员工出车拍照相关类型定义 ====================

  /** 打卡照片分组 */
  interface CheckInPhotos {
    /** 车辆外观照片，最多9张 */
    vehicleExterior?: string[];
    /** 服务人员照片，最多9张 */
    serviceStaff?: string[];
    /** 车内情况照片，最多9张 */
    vehicleInterior?: string[];
  }

  /** 员工打卡记录 */
  interface EmployeeCheckin {
    /** 打卡记录ID */
    id: number;
    /** 员工ID */
    employeeId: number;
    /** 分组照片对象 */
    photos: CheckInPhotos;
    /** 打卡描述 */
    description?: string;
    /** 打卡地址 */
    address?: string;
    /** 经度 */
    longitude?: string;
    /** 纬度 */
    latitude?: string;
    /** 打卡时间 */
    checkInTime: Date;
    /** 创建时间 */
    createdAt?: Date;
    /** 更新时间 */
    updatedAt?: Date;
    /** 关联的员工信息 */
    employee?: Employee;
  }

  /** 员工打卡统计信息 */
  interface EmployeeCheckinStats {
    /** 总打卡次数 */
    totalCount: number;
    /** 今日打卡次数 */
    todayCount: number;
    /** 本周打卡次数 */
    weekCount: number;
    /** 本月打卡次数 */
    monthCount: number;
    /** 活跃员工数 */
    activeEmployeeCount: number;
    /** 今日活跃员工数 */
    todayActiveEmployeeCount: number;
  }

  /** 员工个人打卡统计 */
  interface EmployeePersonalCheckinStats {
    /** 员工ID */
    employeeId: number;
    /** 员工姓名 */
    employeeName: string;
    /** 总打卡次数 */
    totalCount: number;
    /** 今日打卡次数 */
    todayCount: number;
    /** 本周打卡次数 */
    weekCount: number;
    /** 本月打卡次数 */
    monthCount: number;
    /** 最后打卡时间 */
    lastCheckinTime?: string;
  }

  /** 员工最后打卡信息 */
  interface EmployeeLastCheckin {
    /** 员工ID */
    employeeId: number;
    /** 最后一次打卡时间，如果从未打卡则为null */
    lastCheckInTime: string | null;
    /** 最后一次打卡记录ID，如果从未打卡则为null */
    lastCheckInId: number | null;
    /** 总打卡次数 */
    totalCheckInCount: number;
    /** 今日打卡次数 */
    todayCheckInCount: number;
  }

  // ==================== 订单金额异常检查相关类型定义 ====================

  /** 订单金额异常检查结果 */
  interface OrderAmountAnomalyCheckResult {
    anomalyCount: number;
    threshold: number;
    anomalies: Array<{
      orderId: number;
      orderSn: string;
      anomalyType: string;
      description: string;
      severity: number;
      canAutoFix: boolean;
      calculatedOriginalPrice: number;
      anomalyAmount: number;
      fixSuggestion?: string;
      currentData: {
        originalPrice: number;
        totalFee: number;
        cardDeduction: number;
        couponDeduction: number;
      };
    }>;
  }

  /** 修复建议 */
  interface FixSuggestion {
    suggestionId: string;
    type: "fix_original_price" | "fix_total_fee" | "fix_discount";
    title: string;
    description: string;
    action: {
      field: string;
      value: number;
      reason: string;
    };
    risk: 'low' | 'medium' | 'high';
    recommended: boolean;
    warning?: string;
  }

  /** 修复建议结果 */
  interface FixSuggestionsResult {
    anomalyCount: number;
    suggestions: Array<{
      orderId: number;
      orderSn: string;
      anomalyType: string;
      description: string;
      severity: number;
      currentData: {
        originalPrice: number;
        totalFee: number;
        cardDeduction: number;
        couponDeduction: number;
      };
      calculatedOriginalPrice: number;
      expectedTotalFee: number;
      suggestions: FixSuggestion[];
      manualReviewRequired: boolean;
    }>;
  }

  /** 应用修复方案请求 */
  interface ApplyFixRequest {
    orderId: number;
    suggestionId: string;
    operatorId: number;
    operatorName: string;
    remark?: string;
    confirmRisk?: boolean;
  }

  /** 应用修复方案结果 */
  interface ApplyFixResult {
    success: boolean;
    message: string;
    orderId: number;
    fixType: string;
    beforeData: {
      originalPrice: number;
      totalFee: number;
      cardDeduction: number;
      couponDeduction: number;
    };
    afterData: {
      originalPrice: number;
      totalFee: number;
      cardDeduction: number;
      couponDeduction: number;
    };
  }

  /** 批量检查结果 */
  interface BatchCheckResult {
    checkedCount: number;
    anomalyCount: number;
    createdRecords: number;
    message: string;
  }

  /** 异常记录列表结果 */
  interface AnomalyRecordsResult {
    total: number;
    records: Array<{
      id: number;
      orderId: number;
      orderSn: string;
      anomalyType: string;
      description: string;
      severity: number;
      processStatus: string;
      canAutoFix: boolean;
      anomalyAmount: number;
      fixSuggestion?: string;
      createdAt: string;
      processedAt?: string;
      operatorName?: string;
      currentData: {
        originalPrice: number;
        totalFee: number;
        cardDeduction: number;
        couponDeduction: number;
      };
      suggestedData?: {
        originalPrice: number;
        totalFee: number;
        cardDeduction: number;
        couponDeduction: number;
      };
    }>;
  }

  /** 修复结果 */
  interface FixResult {
    success: boolean;
    result: string;
    message: string;
  }

  /** 批量修复结果 */
  interface BatchFixResult {
    totalAttempts: number;
    successCount: number;
    failedCount: number;
    results: Array<{
      recordId: number;
      success: boolean;
      message: string;
    }>;
  }

  /** 回退结果 */
  interface RevertResult {
    success: boolean;
    message: string;
  }

  /** 忽略结果 */
  interface IgnoreResult {
    success: boolean;
    message: string;
  }

  /** 异常统计报告 */
  interface AnomalyStatistics {
    totalAnomalies: number;
    statusStatistics: Array<{
      processStatus: string;
      count: number;
    }>;
    typeStatistics: Array<{
      anomalyType: string;
      count: number;
    }>;
    autoFixStatistics: {
      autoFixableCount: number;
      autoFixedCount: number;
      autoFixSuccessRate: string;
    };
  }

  /** 修复日志结果 */
  interface FixLogsResult {
    total: number;
    logs: Array<{
      id: number;
      anomalyRecordId: number;
      orderId: number;
      orderSn: string;
      operationType: string;
      result: string;
      beforeData: any;
      afterData: any;
      operatorId: number;
      operatorName: string;
      operatedAt: string;
      remark?: string;
    }>;
  }

  /** 异常报告 */
  interface AnomalyReport {
    reportTime: string;
    summary: {
      totalAnomalies: number;
      pendingCount: number;
      fixedCount: number;
      ignoredCount: number;
    };
    details: Array<{
      orderId: number;
      orderSn: string;
      anomalyType: string;
      severity: number;
      description: string;
      processStatus: string;
      detectedAt: string;
    }>;
  }

  // ==================== 员工动作记录相关类型定义 ====================

  /** 员工动作记录 */
  interface EmployeeActionLog {
    /** 记录ID */
    id: number;
    /** 订单ID */
    orderId: number;
    /** 订单编号 */
    orderSn: string;
    /** 订单状态 */
    orderStatus: string;
    /** 预约服务时间 */
    serviceTime: string;
    /** 订单总费用 */
    totalFee: number;
    /** 客户信息 */
    customer: {
      id: number;
      nickname: string;
      phone: string;
    };
    /** 员工信息 */
    employee: {
      id: number;
      name: string;
      phone: string;
    };
    /** 动作类型 */
    changeType: string;
    /** 动作类型标签 */
    changeTypeLabel: string;
    /** 描述 */
    description?: string;
    /** 动作执行时间 */
    actionTime: string;
  }

  /** 员工动作统计概览 */
  interface EmployeeActionOverview {
    /** 总动作数 */
    totalActions: number;
    /** 动作统计 */
    actionStats: Array<{
      changeType: string;
      changeTypeLabel: string;
      count: number;
    }>;
  }

  /** 按员工统计动作记录 */
  interface EmployeeActionStatsByEmployee {
    /** 员工ID */
    employeeId: number;
    /** 员工姓名 */
    employeeName: string;
    /** 员工手机号 */
    employeePhone: string;
    /** 总动作数 */
    totalActions: number;
    /** 动作分解统计 */
    actionBreakdown: Array<{
      changeType: string;
      changeTypeLabel: string;
      count: number;
    }>;
  }

  /** 员工动作时间线 */
  interface EmployeeActionTimeline {
    /** 记录ID */
    id: number;
    /** 订单ID */
    orderId: number;
    /** 订单编号 */
    orderSn: string;
    /** 动作类型 */
    changeType: string;
    /** 动作类型标签 */
    changeTypeLabel: string;
    /** 描述 */
    description?: string;
    /** 动作执行时间 */
    actionTime: string;
  }

  // ==================== 异常价格检测相关类型定义 ====================

  /** 异常价格订单 */
  interface AbnormalPriceOrder {
    id: number;
    sn: string;
    originalPrice: number;
    totalFee: number;
    cardDeduction: number;
    couponDeduction: number;
    priceDifference: number;
    differenceRate: number;
    abnormalReason: string[];
    calculatedPrice: number;
    isConfirmedAbnormal: boolean;
  }

  /** 异常价格检查结果 */
  interface AbnormalPriceCheckResult {
    abnormalCount: number;
    threshold: number;
    abnormalOrders: AbnormalPriceOrder[];
  }

  /** 异常价格详情 */
  interface AbnormalPriceDetail {
    order: {
      id: number;
      sn: string;
      originalPrice: number;
      totalFee: number;
      cardDeduction: number;
      couponDeduction: number;
      status: string;
      orderTime: string;
    };
    priceAnalysis: {
      calculatedPrice: number;
      priceDifference: number;
      differenceRate: number;
      abnormalReason: string[];
      isConfirmedAbnormal: boolean;
    };
    orderDetails: Array<{
      id: number;
      serviceId: number;
      serviceName: string;
      servicePrice: number;
      currentServicePrice: number;
      priceDifference: number;
    }>;
  }

  /** 异常价格修正结果 */
  interface AbnormalPriceFixResult {
    fixedCount: number;
    message: string;
  }

  /** 异常价格阈值设置结果 */
  interface AbnormalPriceThresholdResult {
    threshold: number;
    message: string;
  }

  /** 异常价格统计报告 */
  interface AbnormalPriceReport {
    threshold: number;
    totalAbnormalCount: number;
    confirmedAbnormalCount: number;
    suspiciousCount: number;
    reasonStatistics: Array<{
      reason: string;
      count: number;
      percentage: string;
    }>;
    topAbnormalOrders: AbnormalPriceOrder[];
  }

  // ==================== 收入统计相关类型定义 ====================

  /** 收入概览统计 */
  interface RevenueOverviewStats {
    /** 有效收入总额（已完成+已评价） */
    totalRevenue: number;
    /** 有效订单原价总额 */
    totalOriginalPrice: number;
    /** 总实付金额（所有状态） */
    totalPaidAmount: number;
    /** 总退款金额 */
    totalRefundedAmount: number;
    /** 总优惠金额 */
    totalDiscount: number;
    /** 净收入（有效收入-退款） */
    netRevenue: number;
    /** 优惠率（%） */
    discountRate: string;
    /** 主订单数据 */
    mainOrder: {
      /** 主订单总数（所有状态） */
      orderCount: number;
      /** 主订单有效收入 */
      effectiveRevenue: number;
      /** 主订单实付金额 */
      paidAmount: number;
      /** 主订单退款金额 */
      refundedAmount: number;
      /** 主订单原价总额 */
      totalOriginalPrice: number;
      /** 有效主订单原价 */
      effectiveOriginalPrice: number;
      /** 主订单优惠金额 */
      totalDiscount: number;
      /** 主订单平均价值 */
      avgOrderValue: string;
    };
    /** 追加服务数据 */
    additionalService: {
      /** 追加服务订单总数 */
      orderCount: number;
      /** 追加服务有效收入 */
      effectiveRevenue: number;
      /** 追加服务实付金额 */
      paidAmount: number;
      /** 追加服务退款金额 */
      refundedAmount: number;
      /** 追加服务原价总额 */
      totalOriginalPrice: number;
      /** 有效追加服务原价 */
      effectiveOriginalPrice: number;
      /** 追加服务优惠金额 */
      totalDiscount: number;
      /** 追加服务平均价值 */
      avgOrderValue: string;
    };
    /** 总订单数（所有状态） */
    totalOrderCount: number;
    /** 有效订单数（已完成+已评价） */
    effectiveOrderCount: number;
    /** 退款订单数（退款中+已退款） */
    refundedOrderCount: number;
    /** 平均订单价值 */
    avgOrderValue: string;
  }

  /** 收入趋势统计 */
  interface RevenueTrendStats {
    /** 时间周期 */
    period: string;
    /** 主订单数据 */
    mainOrder?: {
      orderCount: number;
      totalRevenue: number;
      totalOriginalPrice: number;
      totalDiscount: number;
    };
    /** 追加服务数据 */
    additionalService?: {
      orderCount: number;
      totalRevenue: number;
      totalOriginalPrice: number;
      totalDiscount: number;
    };
    /** 当期总收入 */
    totalRevenue: number;
    /** 当期总原价 */
    totalOriginalPrice: number;
    /** 当期总优惠 */
    totalDiscount: number;
    /** 当期总订单数 */
    totalOrderCount: number;
  }

  /** 服务收入统计 */
  interface RevenueServiceStats {
    /** 服务ID */
    serviceId: number;
    /** 服务名称 */
    serviceName: string;
    /** 服务类型 */
    serviceType: string;
    /** 服务类型ID */
    serviceTypeId: number;
    /** 总订单数（所有状态） */
    totalOrderCount: number;
    /** 有效订单数（已完成+已评价） */
    effectiveOrderCount: number;
    /** 退款订单数（退款中+已退款） */
    refundedOrderCount: number;
    /** 总原价 */
    totalOriginalPrice: number;
    /** 有效收入（已完成+已评价） */
    effectiveRevenue: number;
    /** 退款金额 */
    refundedAmount: number;
    /** 净收入（有效收入-退款） */
    netRevenue: number;
    /** 平均收入（基于有效订单） */
    avgRevenue: number;
    /** 服务基础价格 */
    basePrice: number;
  }

  /** 员工收入统计 */
  interface RevenueEmployeeStats {
    /** 员工ID */
    employeeId: number;
    /** 员工信息 */
    employee: {
      id: number;
      name: string;
      phone: string;
      avatar?: string;
      rating?: number;
    };
    /** 主订单数据 */
    mainOrder: {
      /** 主订单总数 */
      orderCount: number;
      /** 主订单有效收入 */
      effectiveRevenue: number;
      /** 主订单实付金额 */
      paidAmount: number;
      /** 主订单退款金额 */
      refundedAmount: number;
      /** 主订单原价总额 */
      totalOriginalPrice: number;
      /** 有效主订单原价 */
      effectiveOriginalPrice: number;
      /** 主订单优惠金额 */
      totalDiscount: number;
      /** 主订单平均收入 */
      avgRevenue: number;
    };
    /** 追加服务数据 */
    additionalService: {
      /** 追加服务订单总数 */
      orderCount: number;
      /** 追加服务有效收入 */
      effectiveRevenue: number;
      /** 追加服务实付金额 */
      paidAmount: number;
      /** 追加服务退款金额 */
      refundedAmount: number;
      /** 追加服务原价总额 */
      totalOriginalPrice: number;
      /** 有效追加服务原价 */
      effectiveOriginalPrice: number;
      /** 追加服务优惠金额 */
      totalDiscount: number;
    };
    /** 员工总有效收入 */
    totalRevenue: number;
    /** 员工总订单数 */
    totalOrderCount: number;
    /** 员工平均收入 */
    avgRevenue: string;
  }

  /** 收入构成分析 */
  interface RevenueCompositionStats {
    /** 收入构成 */
    revenueComposition: {
      mainOrderRevenue: number;
      mainOrderPercentage: string;
      additionalServiceRevenue: number;
      additionalServicePercentage: string;
    };
    /** 优惠构成 */
    discountComposition: {
      totalDiscount: number;
      discountRate: string;
      mainOrderDiscount: number;
      additionalServiceDiscount: number;
    };
    /** 订单数量构成 */
    orderComposition: {
      totalOrderCount: number;
      mainOrderCount: number;
      mainOrderPercentage: string;
      additionalServiceCount: number;
      additionalServicePercentage: string;
    };
  }

  /** 分页响应通用类型 */
  interface PaginatedResponse<T> {
    /** 数据列表 */
    list: T[];
    /** 总记录数 */
    total: number;
    /** 当前页码 */
    page: number;
    /** 每页数量 */
    pageSize: number;
  }
}
