import * as revenueStatistics from '@/services/revenue-statistics';
import { formatAmount } from '@/utils/format';
import { Line } from '@ant-design/plots';
import { ProCard } from '@ant-design/pro-components';
import { Select, message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface TrendChartProps {
  dateRange: [Dayjs, Dayjs];
}

const TrendChart: React.FC<TrendChartProps> = ({ dateRange }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.RevenueTrendStats[]>([]);
  const [periodType, setPeriodType] = useState<'day' | 'week' | 'month'>('day');

  const fetchData = async () => {
    if (!dateRange || !dateRange[0] || !dateRange[1]) return;

    setLoading(true);
    try {
      const { errCode, msg, data: result } = await revenueStatistics.trend({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        periodType,
      });

      if (errCode) {
        message.error(msg || '获取收入趋势数据失败');
        return;
      }

      setData(result || []);
    } catch (error) {
      console.error('获取收入趋势数据失败:', error);
      message.error('获取收入趋势数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange, periodType]);

  // 转换数据格式用于图表
  const chartData = data.flatMap((item) => [
    {
      period: item.period,
      type: '总收入',
      value: item.totalRevenue,
    },
    {
      period: item.period,
      type: '总原价',
      value: item.totalOriginalPrice,
    },
    {
      period: item.period,
      type: '总优惠',
      value: item.totalDiscount,
    },
    {
      period: item.period,
      type: '主订单收入',
      value: item.mainOrder?.totalRevenue || 0,
    },
    {
      period: item.period,
      type: '追加服务收入',
      value: item.additionalService?.totalRevenue || 0,
    },
  ]);

  const config = {
    data: chartData,
    xField: 'period',
    yField: 'value',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
    color: ['#1890ff', '#722ed1', '#ff4d4f', '#52c41a', '#faad14'],
    legend: {
      position: 'top' as const,
    },
    tooltip: {
      shared: true,
      showCrosshairs: true,
      formatter: (datum: any) => ({
        name: datum.type,
        value: `¥${formatAmount(datum.value)}`,
      }),
    },
    yAxis: {
      label: {
        formatter: (text: string) => `¥${text}`,
      },
    },
  };

  return (
    <ProCard
      title="收入趋势分析"
      loading={loading}
      extra={
        <Select
          value={periodType}
          onChange={setPeriodType}
          style={{ width: 100 }}
          options={[
            { label: '按天', value: 'day' },
            { label: '按周', value: 'week' },
            { label: '按月', value: 'month' },
          ]}
        />
      }
    >
      <Line {...config} height={300} />
    </ProCard>
  );
};

export default TrendChart;
