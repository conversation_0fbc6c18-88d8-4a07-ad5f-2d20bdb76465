import { applyFix, checkOrderAmountAnomaly } from '@/services/data-consistency';
import { formatAmount } from '@/utils/format';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  SearchOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  Empty,
  Form,
  Input,
  InputNumber,
  List,
  Modal,
  Row,
  Space,
  Spin,
  Tag,
  Tooltip,
  Typography,
  message,
} from 'antd';
import React, { useState } from 'react';

const { TextArea } = Input;

const { Title, Text } = Typography;

interface AnomalyDetectionPanelProps {
  loading: boolean;
  currentUser?: API.User;
}

/**
 * 异常检测面板组件
 */
const AnomalyDetectionPanel: React.FC<AnomalyDetectionPanelProps> = ({
  loading,
  currentUser,
}) => {
  const [form] = Form.useForm();
  const [checkParams, setCheckParams] = useState({
    clearExistingRecords: false,
    orderId: undefined as number | undefined,
  });

  // 修复建议相关状态
  const [suggestionsLoading, setSuggestionsLoading] = useState(false);
  const [applyLoading, setApplyLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<API.FixSuggestionsResult>();
  const [selectedSuggestion, setSelectedSuggestion] = useState<any>(null);
  const [selectedFixType, setSelectedFixType] = useState<string>('');
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);

  // 执行异常检测并获取修复建议
  const handleCheck = async () => {
    setSuggestionsLoading(true);
    try {
      const res = await checkOrderAmountAnomaly(checkParams);
      if (!res.errCode) {
        setSuggestions(res.data);
        if (res.data?.anomalyCount === 0) {
          message.info('未发现需要修复的异常');
        } else {
          message.success(
            `检查完成，发现 ${res.data?.anomalyCount || 0} 个异常`,
          );
        }
      } else {
        message.error(res.msg || '异常检查失败');
      }
    } catch (error) {
      console.error('异常检查失败:', error);
      message.error('异常检查失败');
    } finally {
      setSuggestionsLoading(false);
    }
  };

  // 应用修复方案
  const handleApplyFix = async (values: any) => {
    if (!selectedSuggestion || !selectedFixType || selectedFixType === '') {
      message.error('请选择修复方案');
      return;
    }

    const fixSuggestion = selectedSuggestion.suggestions.find(
      (item: any) => item.type === selectedFixType,
    );

    if (!fixSuggestion) {
      message.error('修复方案不存在');
      return;
    }

    const params: API.ApplyFixRequest = {
      orderId: selectedSuggestion.orderId,
      fixType: selectedFixType as
        | 'fix_original_price'
        | 'fix_total_fee'
        | 'fix_discount',
      operatorId: currentUser?.id || 0,
      operatorName: currentUser?.nickname || currentUser?.username || '管理员',
      remark: values.remark || `应用${fixSuggestion.title}修复方案`,
      confirmRisk: fixSuggestion.risk === 'high',
    };

    // 根据修复类型设置不同的参数
    if (selectedFixType === 'fix_original_price') {
      params.value = fixSuggestion.action.value;
    } else if (selectedFixType === 'fix_total_fee') {
      params.value = fixSuggestion.action.value;
    } else if (selectedFixType === 'fix_discount') {
      params.cardDeduction =
        values.cardDeduction || selectedSuggestion.currentData.cardDeduction;
      params.couponDeduction =
        values.couponDeduction ||
        selectedSuggestion.currentData.couponDeduction;
    }

    setApplyLoading(true);
    try {
      const res = await applyFix(params);
      if (!res.errCode) {
        message.success('修复方案应用成功');
        setConfirmModalVisible(false);
        // 重新检查获取最新数据
        handleCheck();
      } else {
        message.error(res.msg || '应用修复方案失败');
      }
    } catch (error) {
      console.error('应用修复方案失败:', error);
      message.error('应用修复方案失败');
    } finally {
      setApplyLoading(false);
    }
  };

  // 打开确认对话框
  const showConfirmModal = (suggestion: any, fixType: string) => {
    setSelectedSuggestion(suggestion);
    setSelectedFixType(fixType);
    setConfirmModalVisible(true);
  };

  // 渲染风险等级标签
  const renderRiskTag = (risk: string) => {
    switch (risk) {
      case 'low':
        return <Tag color="success">低风险</Tag>;
      case 'medium':
        return <Tag color="warning">中等风险</Tag>;
      case 'high':
        return <Tag color="error">高风险</Tag>;
      default:
        return <Tag>未知风险</Tag>;
    }
  };

  // 渲染异常类型标签
  const renderAnomalyTypeTag = (type: string) => {
    switch (type) {
      case 'price_mismatch':
        return <Tag color="blue">原价不匹配</Tag>;
      case 'calculation_error':
        return <Tag color="red">计算错误</Tag>;
      case 'missing_original_price':
        return <Tag color="orange">原价缺失</Tag>;
      case 'discount_anomaly':
        return <Tag color="purple">优惠异常</Tag>;
      default:
        return <Tag>其他异常</Tag>;
    }
  };

  // 渲染严重程度标签
  const renderSeverityTag = (severity: number) => {
    switch (severity) {
      case 1:
        return <Tag color="blue">轻微</Tag>;
      case 2:
        return <Tag color="cyan">一般</Tag>;
      case 3:
        return <Tag color="orange">中等</Tag>;
      case 4:
        return <Tag color="volcano">严重</Tag>;
      case 5:
        return <Tag color="red">极严重</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  // 获取修复类型的中文名称
  const getFixTypeDisplayName = (type: string) => {
    switch (type) {
      case 'fix_original_price':
        return '修正原价';
      case 'fix_total_fee':
        return '修正实付金额';
      case 'fix_discount':
        return '调整优惠金额';
      default:
        return type;
    }
  };

  // 获取修复类型的图标
  const getFixTypeIcon = (type: string) => {
    switch (type) {
      case 'fix_original_price':
        return '💰';
      case 'fix_total_fee':
        return '💳';
      case 'fix_discount':
        return '🎫';
      default:
        return '🔧';
    }
  };

  return (
    <div>
      {/* 检测参数配置 */}
      <Card title="检测参数配置" size="small" style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="inline"
          initialValues={checkParams}
          onValuesChange={(_, values) => setCheckParams(values)}
        >
          <Form.Item
            label="指定订单ID"
            name="orderId"
            tooltip="可选，指定订单ID只检查该订单"
          >
            <InputNumber
              min={1}
              style={{ width: 150 }}
              placeholder="可选，留空检查所有"
            />
          </Form.Item>
          <Form.Item
            label="清除已有记录"
            name="clearExistingRecords"
            tooltip="检测前是否清除所有已有异常记录"
          >
            <Button
              type={checkParams.clearExistingRecords ? 'primary' : 'default'}
              size="small"
              onClick={() =>
                setCheckParams({
                  ...checkParams,
                  clearExistingRecords: !checkParams.clearExistingRecords,
                })
              }
            >
              {checkParams.clearExistingRecords ? '是' : '否'}
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 检测操作 */}
      <Card title="异常检测操作" size="small">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Title level={5}>
              <InfoCircleOutlined
                style={{ marginRight: 8, color: '#1890ff' }}
              />
              快速检测
            </Title>
            <Text
              type="secondary"
              style={{ display: 'block', marginBottom: 16 }}
            >
              检查订单金额异常并生成详细的修复建议，包含主订单和追加服务的完整计算。
            </Text>
            <Space>
              <Tooltip title="检查订单金额异常并生成修复建议">
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  loading={loading}
                  onClick={handleCheck}
                >
                  检查异常并生成建议
                </Button>
              </Tooltip>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 修复建议列表 */}
      <Card title="修复建议列表" size="small" style={{ marginTop: 16 }}>
        <Spin spinning={suggestionsLoading}>
          {suggestions?.anomalyCount ? (
            <List
              itemLayout="vertical"
              dataSource={suggestions.suggestions}
              renderItem={(item) => (
                <List.Item
                  key={item.orderId}
                  extra={
                    <Space direction="vertical" align="end">
                      <Space>
                        {renderAnomalyTypeTag(item.anomalyType)}
                        {renderSeverityTag(item.severity)}
                      </Space>
                      <Text strong>订单号: {item.orderSn}</Text>
                    </Space>
                  }
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>异常描述: </Text>
                        <Text type="danger">{item.description}</Text>
                      </Space>
                    }
                    description={
                      <Descriptions size="small" column={4}>
                        <Descriptions.Item label="原价">
                          {formatAmount(item.currentData.originalPrice)}元
                        </Descriptions.Item>
                        <Descriptions.Item label="实付金额">
                          {formatAmount(item.currentData.totalFee)}元
                        </Descriptions.Item>
                        <Descriptions.Item label="权益卡抵扣">
                          {formatAmount(item.currentData.cardDeduction)}元
                        </Descriptions.Item>
                        <Descriptions.Item label="代金券抵扣">
                          {formatAmount(item.currentData.couponDeduction)}元
                        </Descriptions.Item>
                      </Descriptions>
                    }
                  />
                  <Divider orientation="left">修复方案</Divider>
                  <List
                    size="small"
                    dataSource={item.suggestions}
                    renderItem={(suggestion) => (
                      <List.Item
                        actions={[
                          <Button
                            key="apply"
                            type="primary"
                            size="small"
                            onClick={() =>
                              showConfirmModal(item, suggestion.type)
                            }
                          >
                            应用此方案
                          </Button>,
                        ]}
                      >
                        <List.Item.Meta
                          avatar={
                            suggestion.risk === 'high' ? (
                              <WarningOutlined
                                style={{ color: '#f5222d', fontSize: 20 }}
                              />
                            ) : suggestion.risk === 'medium' ? (
                              <ExclamationCircleOutlined
                                style={{ color: '#faad14', fontSize: 20 }}
                              />
                            ) : (
                              <CheckCircleOutlined
                                style={{ color: '#52c41a', fontSize: 20 }}
                              />
                            )
                          }
                          title={
                            <Space>
                              <Text strong>
                                {getFixTypeIcon(suggestion.type)}{' '}
                                {getFixTypeDisplayName(suggestion.type)}
                              </Text>
                              {renderRiskTag(suggestion.risk)}
                              {suggestion.recommended && (
                                <Tag color="green">推荐</Tag>
                              )}
                            </Space>
                          }
                          description={
                            <>
                              <Typography.Paragraph>
                                <Text strong>修复说明：</Text>
                                {suggestion.description}
                              </Typography.Paragraph>

                              {/* 详细的修复信息 */}
                              <div style={{ background: '#f6f8fa', padding: '12px', borderRadius: '6px', marginBottom: '12px' }}>
                                <Typography.Paragraph style={{ marginBottom: '8px' }}>
                                  <Text strong style={{ color: '#1890ff' }}>📋 修复详情：</Text>
                                </Typography.Paragraph>

                                {/* 风险等级 */}
                                <Typography.Paragraph style={{ marginBottom: '8px' }}>
                                  <Text strong>🔒 风险等级：</Text>
                                  {suggestion.risk === 'high' ? (
                                    <Text type="danger">高 🔴 （需要银行流水确认）</Text>
                                  ) : suggestion.risk === 'medium' ? (
                                    <Text style={{ color: '#faad14' }}>中等 🟡 （需要业务确认）</Text>
                                  ) : (
                                    <Text type="success">低 🟢 （相对安全）</Text>
                                  )}
                                </Typography.Paragraph>

                                {/* 适用场景 */}
                                <Typography.Paragraph style={{ marginBottom: '8px' }}>
                                  <Text strong>🎯 适用场景：</Text>
                                  {(() => {
                                    const fixType = suggestion.type as string;
                                    switch (fixType) {
                                      case 'fix_original_price':
                                        return <Text>原价缺失或需要根据实付金额反推原价</Text>;
                                      case 'fix_total_fee':
                                        return <Text>确认银行实际扣款，修改系统记录的实付金额</Text>;
                                      case 'fix_discount':
                                        return <Text>优惠金额计算错误，超过原价或计算不准</Text>;
                                      default:
                                        return <Text>根据具体异常情况进行修复</Text>;
                                    }
                                  })()}
                                </Typography.Paragraph>

                                {/* 操作方式 */}
                                <Typography.Paragraph style={{ marginBottom: '8px' }}>
                                  <Text strong>⚙️ 操作方式：</Text>
                                  {suggestion.action?.reason && (
                                    <Text>{suggestion.action.reason}</Text>
                                  )}
                                </Typography.Paragraph>

                                {/* 修正值 */}
                                {suggestion.action?.value && (
                                  <Typography.Paragraph style={{ marginBottom: '8px' }}>
                                    <Text strong>💰 修正值：</Text>
                                    <Text strong style={{ color: '#1890ff', fontSize: '16px' }}>
                                      {formatAmount(suggestion.action.value)}元
                                    </Text>
                                  </Typography.Paragraph>
                                )}

                                {/* 推荐指数 */}
                                <Typography.Paragraph style={{ marginBottom: '8px' }}>
                                  <Text strong>⭐ 推荐指数：</Text>
                                  {suggestion.recommended ? (
                                    <Text type="success">⭐⭐⭐ 推荐使用</Text>
                                  ) : suggestion.risk === 'medium' ? (
                                    <Text style={{ color: '#faad14' }}>⭐⭐ 谨慎使用</Text>
                                  ) : (
                                    <Text type="danger">⭐ 高风险操作</Text>
                                  )}
                                </Typography.Paragraph>

                                {/* 注意事项 */}
                                {(() => {
                                  const fixType = suggestion.type as string;
                                  switch (fixType) {
                                    case 'fix_total_fee':
                                      return (
                                        <Typography.Paragraph style={{ marginBottom: '0' }}>
                                          <Text strong style={{ color: '#f5222d' }}>⚠️ 注意事项：</Text>
                                          <Text type="danger">
                                            实付金额与银行流水相关，修改前请确认银行实际扣款金额
                                          </Text>
                                        </Typography.Paragraph>
                                      );
                                    case 'fix_original_price':
                                      return (
                                        <Typography.Paragraph style={{ marginBottom: '0' }}>
                                          <Text strong style={{ color: '#f5222d' }}>⚠️ 注意事项：</Text>
                                          <Text type="danger">
                                            仅在原价缺失或明确需要修正时使用
                                          </Text>
                                        </Typography.Paragraph>
                                      );
                                    case 'fix_discount':
                                      return (
                                        <Typography.Paragraph style={{ marginBottom: '0' }}>
                                          <Text strong style={{ color: '#faad14' }}>⚠️ 注意事项：</Text>
                                          <Text style={{ color: '#faad14' }}>
                                            修改后需要验证业务逻辑一致性
                                          </Text>
                                        </Typography.Paragraph>
                                      );
                                    default:
                                      return null;
                                  }
                                })()}
                              </div>

                              {suggestion.warning && (
                                <Alert
                                  message={suggestion.warning}
                                  type="warning"
                                  showIcon
                                  style={{ marginTop: '8px' }}
                                />
                              )}
                            </>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </List.Item>
              )}
            />
          ) : (
            <Empty description="暂无修复建议，请先执行异常检测" />
          )}
        </Spin>
      </Card>

      {/* 检测说明 */}
      <Card title="检测规则说明" size="small" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Title level={5}>检测维度</Title>
            <ul>
              <li>
                <strong>支付计算逻辑：</strong>检测支付计算是否正确
              </li>
              <li>
                <strong>核心公式：</strong>实付金额 = 原价 - 权益卡抵扣 -
                代金券抵扣
              </li>
              <li>
                <strong>优惠合理性：</strong>优惠总额不能超过订单原价
              </li>
              <li>
                <strong>原价完整性：</strong>原价不能为空或0
              </li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={5}>修复方案</Title>
            <ul>
              <li>
                🟡 <strong>修正原价：</strong>
                风险中等，适用于原价缺失或明确需要修正的情况
              </li>
              <li>
                🟡 <strong>调整优惠：</strong>风险中等，需要确认业务逻辑
              </li>
              <li>
                🔴 <strong>修正实付：</strong>风险高，需要确认银行流水，谨慎操作
              </li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={5}>设计理念</Title>
            <ul>
              <li>
                <strong>专注支付逻辑：</strong>只检测支付计算的正确性
              </li>
              <li>
                <strong>保护原价：</strong>原价反映订单创建时的实际价格
              </li>
              <li>
                <strong>风险控制：</strong>高风险操作需要额外确认
              </li>
              <li>
                <strong>操作记录：</strong>所有修复操作都有详细日志
              </li>
            </ul>
          </Col>
        </Row>
      </Card>

      {/* 应用修复方案确认对话框 */}
      <Modal
        title="确认应用修复方案"
        open={confirmModalVisible}
        onCancel={() => setConfirmModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedSuggestion && (
          <Form form={form} layout="vertical" onFinish={handleApplyFix}>
            <Alert
              message="操作确认"
              description={
                <>
                  <p>
                    您正在为订单{' '}
                    <Text strong>{selectedSuggestion.orderSn}</Text>{' '}
                    应用修复方案。
                  </p>
                  <p>
                    修复类型:{' '}
                    <Text strong>
                      {getFixTypeIcon(selectedFixType)}{' '}
                      {getFixTypeDisplayName(selectedFixType)}
                    </Text>
                  </p>
                  <p>
                    风险等级:{' '}
                    {renderRiskTag(
                      selectedSuggestion.suggestions.find(
                        (item: any) => item.type === selectedFixType,
                      )?.risk || 'medium',
                    )}
                  </p>
                </>
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {selectedSuggestion.suggestions.find(
              (item: any) => item.type === selectedFixType,
            )?.risk === 'high' && (
              <Alert
                message="高风险操作警告"
                description="此操作为高风险操作，可能影响订单数据完整性。请确认已经充分了解操作后果。"
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Form.Item
              name="remark"
              label="操作备注"
              rules={[{ required: true, message: '请输入操作备注' }]}
            >
              <TextArea rows={3} placeholder="请输入操作备注，说明修复原因" />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" loading={applyLoading}>
                  确认应用
                </Button>
                <Button onClick={() => setConfirmModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default AnomalyDetectionPanel;
