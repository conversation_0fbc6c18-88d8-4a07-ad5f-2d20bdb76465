import {
  BulbOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Al<PERSON>,
  Button,
  Card,
  Col,
  Divider,
  Form,
  InputNumber,
  Row,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import React, { useState } from 'react';

const { Title, Text } = Typography;

interface AnomalyDetectionPanelProps {
  loading: boolean;
  onCheckAnomaly: (params?: any) => void;
  onBatchCheck: (params?: any) => void;
  onGenerateReport: (params?: any) => void;
  onGenerateFixSuggestions?: (params?: any) => void;
}

/**
 * 异常检测面板组件
 */
const AnomalyDetectionPanel: React.FC<AnomalyDetectionPanelProps> = ({
  loading,
  onCheckAnomaly,
  onBatchCheck,
  onGenerateReport,
  onGenerateFixSuggestions,
}) => {
  const [form] = Form.useForm();
  const [checkParams, setCheckParams] = useState({
    clearExistingRecords: false,
    orderId: undefined as number | undefined,
  });

  // 执行异常检测
  const handleCheck = () => {
    onCheckAnomaly(checkParams);
  };

  // 执行批量检查（现在使用同一个接口）
  const handleBatchCheck = () => {
    onBatchCheck(checkParams);
  };

  // 生成异常报告
  const handleGenerateReport = () => {
    onGenerateReport({
      includeDetails: 'true',
    });
  };

  // 生成修复建议
  const handleGenerateFixSuggestions = () => {
    if (onGenerateFixSuggestions) {
      onGenerateFixSuggestions({
        clearExistingRecords: checkParams.clearExistingRecords,
      });
    }
  };

  return (
    <div>
      {/* 检测参数配置 */}
      <Card title="检测参数配置" size="small" style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="inline"
          initialValues={checkParams}
          onValuesChange={(_, values) => setCheckParams(values)}
        >
          <Form.Item
            label="指定订单ID"
            name="orderId"
            tooltip="可选，指定订单ID只检查该订单"
          >
            <InputNumber
              min={1}
              style={{ width: 150 }}
              placeholder="可选，留空检查所有"
            />
          </Form.Item>
          <Form.Item
            label="清除已有记录"
            name="clearExistingRecords"
            tooltip="检测前是否清除所有已有异常记录"
          >
            <Button
              type={checkParams.clearExistingRecords ? 'primary' : 'default'}
              size="small"
              onClick={() =>
                setCheckParams({
                  ...checkParams,
                  clearExistingRecords: !checkParams.clearExistingRecords,
                })
              }
            >
              {checkParams.clearExistingRecords ? '是' : '否'}
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 检测操作 */}
      <Card title="异常检测操作" size="small">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Title level={5}>
              <InfoCircleOutlined
                style={{ marginRight: 8, color: '#1890ff' }}
              />
              快速检测
            </Title>
            <Text
              type="secondary"
              style={{ display: 'block', marginBottom: 16 }}
            >
              检查订单金额异常并生成详细的修复建议，包含主订单和追加服务的完整计算。
            </Text>
            <Space>
              <Tooltip title="检查订单金额异常并生成修复建议">
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  loading={loading}
                  onClick={handleCheck}
                >
                  检查异常并生成建议
                </Button>
              </Tooltip>
            </Space>
          </Col>

          <Col span={24}>
            <Divider />
            <Title level={5}>
              <FileTextOutlined style={{ marginRight: 8, color: '#fa8c16' }} />
              异常报告
            </Title>
            <Text
              type="secondary"
              style={{ display: 'block', marginBottom: 16 }}
            >
              生成详细的异常问题清单报告，包含异常详情和处理建议。
            </Text>
            <Space>
              <Tooltip title="生成异常问题清单报告">
                <Button
                  icon={<FileTextOutlined />}
                  loading={loading}
                  onClick={handleGenerateReport}
                >
                  生成异常报告
                </Button>
              </Tooltip>
              {onGenerateFixSuggestions && (
                <Tooltip title="生成订单金额修复建议">
                  <Button
                    type="primary"
                    icon={<BulbOutlined />}
                    loading={loading}
                    onClick={handleGenerateFixSuggestions}
                    style={{
                      backgroundColor: '#722ed1',
                      borderColor: '#722ed1',
                    }}
                  >
                    生成修复建议
                  </Button>
                </Tooltip>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 检测说明 */}
      <Card title="检测规则说明" size="small" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Title level={5}>检测维度</Title>
            <ul>
              <li>
                <strong>支付计算逻辑：</strong>检测支付计算是否正确
              </li>
              <li>
                <strong>核心公式：</strong>实付金额 = 原价 - 权益卡抵扣 -
                代金券抵扣
              </li>
              <li>
                <strong>优惠合理性：</strong>优惠总额不能超过订单原价
              </li>
              <li>
                <strong>原价完整性：</strong>原价不能为空或0
              </li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={5}>修复方案</Title>
            <ul>
              <li>
                🟡 <strong>调整优惠：</strong>风险中等，需要确认业务逻辑
              </li>
              <li>
                🔴 <strong>修正实付：</strong>风险高，需要确认银行流水，谨慎操作
              </li>
            </ul>
            <Alert
              message="重要说明"
              description="系统不再提供「修正原价」选项，因为原价应该反映订单创建时的实际价格，不应根据当前服务价格进行调整。"
              type="info"
              showIcon
              style={{ marginTop: 8 }}
            />
            <Title level={5} style={{ marginTop: 16 }}>
              检测范围
            </Title>
            <ul>
              <li>
                ✅ <strong>支付计算错误：</strong>实付金额与计算结果不符
              </li>
              <li>
                ✅ <strong>优惠金额异常：</strong>优惠总额超过原价
              </li>
              <li>
                ✅ <strong>原价缺失：</strong>原价为空或0
              </li>
              <li>
                ❌ <strong>不再检测：</strong>原价与订单明细价格的一致性
              </li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={5}>设计理念</Title>
            <ul>
              <li>
                <strong>专注支付逻辑：</strong>只检测支付计算的正确性
              </li>
              <li>
                <strong>保护原价：</strong>原价反映订单创建时的实际价格
              </li>
              <li>
                <strong>风险控制：</strong>高风险操作需要额外确认
              </li>
              <li>
                <strong>操作记录：</strong>所有修复操作都有详细日志
              </li>
            </ul>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default AnomalyDetectionPanel;
