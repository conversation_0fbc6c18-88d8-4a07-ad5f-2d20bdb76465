import {
  BulbOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  ScanOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Divider,
  Form,
  InputNumber,
  Row,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import React, { useState } from 'react';

const { Title, Text } = Typography;

interface AnomalyDetectionPanelProps {
  loading: boolean;
  onCheckAnomaly: (params?: any) => void;
  onBatchCheck: (params?: any) => void;
  onGenerateReport: (params?: any) => void;
  onGenerateFixSuggestions?: (params?: any) => void;
}

/**
 * 异常检测面板组件
 */
const AnomalyDetectionPanel: React.FC<AnomalyDetectionPanelProps> = ({
  loading,
  onCheckAnomaly,
  onBatchCheck,
  onGenerateReport,
  onGenerateFixSuggestions,
}) => {
  const [form] = Form.useForm();
  const [checkParams, setCheckParams] = useState({
    clearExistingRecords: false,
    orderId: undefined as number | undefined,
  });

  // 执行异常检测
  const handleCheck = () => {
    onCheckAnomaly(checkParams);
  };

  // 执行批量检查（现在使用同一个接口）
  const handleBatchCheck = () => {
    onBatchCheck(checkParams);
  };

  // 生成异常报告
  const handleGenerateReport = () => {
    onGenerateReport({
      includeDetails: 'true',
    });
  };

  // 生成修复建议
  const handleGenerateFixSuggestions = () => {
    if (onGenerateFixSuggestions) {
      onGenerateFixSuggestions({
        clearExistingRecords: checkParams.clearExistingRecords,
      });
    }
  };

  return (
    <div>
      {/* 检测参数配置 */}
      <Card title="检测参数配置" size="small" style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="inline"
          initialValues={checkParams}
          onValuesChange={(_, values) => setCheckParams(values)}
        >
          <Form.Item
            label="指定订单ID"
            name="orderId"
            tooltip="可选，指定订单ID只检查该订单"
          >
            <InputNumber
              min={1}
              style={{ width: 150 }}
              placeholder="可选，留空检查所有"
            />
          </Form.Item>
          <Form.Item
            label="清除已有记录"
            name="clearExistingRecords"
            tooltip="检测前是否清除所有已有异常记录"
          >
            <Button
              type={checkParams.clearExistingRecords ? 'primary' : 'default'}
              size="small"
              onClick={() =>
                setCheckParams({
                  ...checkParams,
                  clearExistingRecords: !checkParams.clearExistingRecords,
                })
              }
            >
              {checkParams.clearExistingRecords ? '是' : '否'}
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 检测操作 */}
      <Card title="异常检测操作" size="small">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Title level={5}>
              <InfoCircleOutlined
                style={{ marginRight: 8, color: '#1890ff' }}
              />
              快速检测
            </Title>
            <Text
              type="secondary"
              style={{ display: 'block', marginBottom: 16 }}
            >
              检查订单金额异常并生成详细的修复建议，包含主订单和追加服务的完整计算。
            </Text>
            <Space>
              <Tooltip title="检查订单金额异常并生成修复建议">
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  loading={loading}
                  onClick={handleCheck}
                >
                  检查异常并生成建议
                </Button>
              </Tooltip>
            </Space>
          </Col>



          <Col span={24}>
            <Divider />
            <Title level={5}>
              <FileTextOutlined style={{ marginRight: 8, color: '#fa8c16' }} />
              异常报告
            </Title>
            <Text
              type="secondary"
              style={{ display: 'block', marginBottom: 16 }}
            >
              生成详细的异常问题清单报告，包含异常详情和处理建议。
            </Text>
            <Space>
              <Tooltip title="生成异常问题清单报告">
                <Button
                  icon={<FileTextOutlined />}
                  loading={loading}
                  onClick={handleGenerateReport}
                >
                  生成异常报告
                </Button>
              </Tooltip>
              {onGenerateFixSuggestions && (
                <Tooltip title="生成订单金额修复建议">
                  <Button
                    type="primary"
                    icon={<BulbOutlined />}
                    loading={loading}
                    onClick={handleGenerateFixSuggestions}
                    style={{ backgroundColor: '#722ed1', borderColor: '#722ed1' }}
                  >
                    生成修复建议
                  </Button>
                </Tooltip>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 检测说明 */}
      <Card title="检测规则说明" size="small" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Title level={5}>检测维度</Title>
            <ul>
              <li>
                <strong>完整计算：</strong>检测主订单和追加服务的完整金额计算
              </li>
              <li>
                <strong>价格一致性：</strong>原价与订单详情价格之和是否匹配
              </li>
              <li>
                <strong>计算逻辑：</strong>实付金额 = 原价 - 权益卡抵扣 - 代金券抵扣
              </li>
              <li>
                <strong>优惠合理性：</strong>优惠总额不能超过订单原价
              </li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={5}>修复方案</Title>
            <ul>
              <li>
                🟢 <strong>修正原价：</strong>风险低，推荐优先使用
              </li>
              <li>
                🟡 <strong>调整优惠：</strong>风险中等，需要确认业务逻辑
              </li>
              <li>
                🔴 <strong>修正实付：</strong>风险高，需要确认银行流水
              </li>
            </ul>
            <Title level={5} style={{ marginTop: 16 }}>
              智能建议
            </Title>
            <ul>
              <li>
                <strong>自动分析：</strong>系统自动分析异常原因
              </li>
              <li>
                <strong>风险评估：</strong>提供详细的风险等级和警告
              </li>
              <li>
                <strong>操作指导：</strong>明确指出修复步骤和注意事项
              </li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={5}>使用建议</Title>
            <ul>
              <li>
                <strong>数据备份：</strong>修复前建议备份订单数据
              </li>
              <li>
                <strong>风险控制：</strong>高风险操作需要额外确认
              </li>
              <li>
                <strong>操作记录：</strong>所有修复操作都有详细日志
              </li>
              <li>
                <strong>验证结果：</strong>修复后验证收入统计数据
              </li>
            </ul>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default AnomalyDetectionPanel;
