import { applyFix, checkOrderAmountAnomaly } from '@/services/data-consistency';
import { formatAmount } from '@/utils/format';
import {
  BulbOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  Al<PERSON>,
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  Empty,
  Form,
  Input,
  List,
  Modal,
  Row,
  Space,
  Spin,
  Tag,
  Tooltip,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface FixSuggestionsPanelProps {
  loading: boolean;
  onRefresh: () => void;
  currentUser?: API.User;
}

/**
 * 修复建议面板组件
 */
const FixSuggestionsPanel: React.FC<FixSuggestionsPanelProps> = ({
  loading,
  onRefresh,
  currentUser,
}) => {
  const [form] = Form.useForm();
  const [suggestionsLoading, setSuggestionsLoading] = useState(false);
  const [applyLoading, setApplyLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<API.FixSuggestionsResult>();
  const [selectedSuggestion, setSelectedSuggestion] = useState<any>(null);
  const [selectedFixType, setSelectedFixType] = useState<"fix_total_fee" | "fix_discount">('');
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);

  // 获取修复建议（使用合并后的检查接口）
  const loadFixSuggestions = async (params?: any) => {
    setSuggestionsLoading(true);
    try {
      const res = await checkOrderAmountAnomaly(params);
      if (!res.errCode) {
        setSuggestions(res.data);
        if (res.data?.anomalyCount === 0) {
          message.info('未发现需要修复的异常');
        }
      } else {
        message.error(res.msg || '获取修复建议失败');
      }
    } catch (error) {
      console.error('获取修复建议失败:', error);
      message.error('获取修复建议失败');
    } finally {
      setSuggestionsLoading(false);
    }
  };

  // 应用修复方案
  const handleApplyFix = async (values: any) => {
    if (!selectedSuggestion || !selectedFixType) {
      message.error('请选择修复方案');
      return;
    }

    const fixSuggestion = selectedSuggestion.suggestions.find(
      (item: any) => item.type === selectedFixType,
    );

    if (!fixSuggestion) {
      message.error('修复方案不存在');
      return;
    }

    const params: API.ApplyFixRequest = {
      orderId: selectedSuggestion.orderId,
      fixType: selectedFixType,
      operatorId: currentUser?.id || 0,
      operatorName: currentUser?.name || '管理员',
      remark: values.remark || `应用${fixSuggestion.title}修复方案`,
      confirmRisk: fixSuggestion.risk === 'high',
    };

    // 根据修复类型设置不同的参数
    if (selectedFixType === 'fix_total_fee') {
      params.value = fixSuggestion.action.value;
    } else if (selectedFixType === 'fix_discount') {
      params.cardDeduction =
        values.cardDeduction || selectedSuggestion.currentData.cardDeduction;
      params.couponDeduction =
        values.couponDeduction ||
        selectedSuggestion.currentData.couponDeduction;
    }

    setApplyLoading(true);
    try {
      const res = await applyFix(params);
      if (!res.errCode) {
        message.success('修复方案应用成功');
        setConfirmModalVisible(false);
        // 刷新数据
        loadFixSuggestions();
        onRefresh();
      } else {
        message.error(res.msg || '应用修复方案失败');
      }
    } catch (error) {
      console.error('应用修复方案失败:', error);
      message.error('应用修复方案失败');
    } finally {
      setApplyLoading(false);
    }
  };

  // 打开确认对话框
  const showConfirmModal = (suggestion: any, fixType: "fix_total_fee" | "fix_discount") => {
    setSelectedSuggestion(suggestion);
    setSelectedFixType(fixType);
    setConfirmModalVisible(true);
  };

  // 渲染风险等级标签
  const renderRiskTag = (risk: string) => {
    switch (risk) {
      case 'medium':
        return <Tag color="warning">中等风险</Tag>;
      case 'high':
        return <Tag color="error">高风险</Tag>;
      default:
        return <Tag>未知风险</Tag>;
    }
  };

  // 渲染异常类型标签
  const renderAnomalyTypeTag = (type: string) => {
    switch (type) {
      case 'price_mismatch':
        return <Tag color="blue">原价不匹配</Tag>;
      case 'calculation_error':
        return <Tag color="red">计算错误</Tag>;
      case 'missing_original_price':
        return <Tag color="orange">原价缺失</Tag>;
      case 'discount_anomaly':
        return <Tag color="purple">优惠异常</Tag>;
      default:
        return <Tag>其他异常</Tag>;
    }
  };

  // 渲染严重程度标签
  const renderSeverityTag = (severity: number) => {
    switch (severity) {
      case 1:
        return <Tag color="blue">轻微</Tag>;
      case 2:
        return <Tag color="cyan">一般</Tag>;
      case 3:
        return <Tag color="orange">中等</Tag>;
      case 4:
        return <Tag color="volcano">严重</Tag>;
      case 5:
        return <Tag color="red">极严重</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  useEffect(() => {
    // 组件挂载时加载修复建议
    loadFixSuggestions();
  }, []);

  return (
    <div>
      {/* 修复建议操作 */}
      <Card
        title="异常检查与修复建议"
        size="small"
        style={{ marginBottom: 16 }}
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Space>
              <Tooltip title="检查订单金额异常并生成修复建议">
                <Button
                  type="primary"
                  icon={<BulbOutlined />}
                  loading={suggestionsLoading}
                  onClick={() => loadFixSuggestions()}
                >
                  检查异常并生成建议
                </Button>
              </Tooltip>
              <Tooltip title="清除现有记录并重新检查">
                <Button
                  icon={<BulbOutlined />}
                  loading={suggestionsLoading}
                  onClick={() =>
                    loadFixSuggestions({ clearExistingRecords: true })
                  }
                >
                  清除并重新检查
                </Button>
              </Tooltip>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 修复建议列表 */}
      <Card title="修复建议列表" size="small">
        <Spin spinning={suggestionsLoading}>
          {suggestions?.anomalyCount ? (
            <List
              itemLayout="vertical"
              dataSource={suggestions.suggestions}
              renderItem={(item) => (
                <List.Item
                  key={item.orderId}
                  extra={
                    <Space direction="vertical" align="end">
                      <Space>
                        {renderAnomalyTypeTag(item.anomalyType)}
                        {renderSeverityTag(item.severity)}
                      </Space>
                      <Text strong>订单号: {item.orderSn}</Text>
                    </Space>
                  }
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>异常描述: </Text>
                        <Text type="danger">{item.description}</Text>
                      </Space>
                    }
                    description={
                      <Descriptions size="small" column={4}>
                        <Descriptions.Item label="原价">
                          {formatAmount(item.currentData.originalPrice)}元
                        </Descriptions.Item>
                        <Descriptions.Item label="实付金额">
                          {formatAmount(item.currentData.totalFee)}元
                        </Descriptions.Item>
                        <Descriptions.Item label="权益卡抵扣">
                          {formatAmount(item.currentData.cardDeduction)}元
                        </Descriptions.Item>
                        <Descriptions.Item label="代金券抵扣">
                          {formatAmount(item.currentData.couponDeduction)}元
                        </Descriptions.Item>
                      </Descriptions>
                    }
                  />
                  <Divider orientation="left">修复方案</Divider>
                  <List
                    size="small"
                    dataSource={item.suggestions}
                    renderItem={(suggestion) => (
                      <List.Item
                        actions={[
                          <Button
                            key="apply"
                            type="primary"
                            size="small"
                            onClick={() =>
                              showConfirmModal(item, suggestion.type)
                            }
                          >
                            应用此方案
                          </Button>,
                        ]}
                      >
                        <List.Item.Meta
                          avatar={
                            suggestion.risk === 'high' ? (
                              <WarningOutlined
                                style={{ color: '#f5222d', fontSize: 20 }}
                              />
                            ) : (
                              <ExclamationCircleOutlined
                                style={{ color: '#faad14', fontSize: 20 }}
                              />
                            )
                          }
                          title={
                            <Space>
                              <Text strong>{suggestion.title}</Text>
                              {renderRiskTag(suggestion.risk)}
                              {suggestion.recommended && (
                                <Tag color="green">推荐</Tag>
                              )}
                            </Space>
                          }
                          description={
                            <>
                              <Paragraph>{suggestion.description}</Paragraph>
                              {suggestion.warning && (
                                <Alert
                                  message={suggestion.warning}
                                  type="warning"
                                  showIcon
                                />
                              )}
                            </>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </List.Item>
              )}
            />
          ) : (
            <Empty description="暂无修复建议" />
          )}
        </Spin>
      </Card>

      {/* 应用修复方案确认对话框 */}
      <Modal
        title="确认应用修复方案"
        open={confirmModalVisible}
        onCancel={() => setConfirmModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedSuggestion && (
          <Form form={form} layout="vertical" onFinish={handleApplyFix}>
            <Alert
              message="操作确认"
              description={
                <>
                  <p>
                    您正在为订单{' '}
                    <Text strong>{selectedSuggestion.orderSn}</Text>{' '}
                    应用修复方案。
                  </p>
                  <p>
                    修复类型:{' '}
                    <Text strong>
                      {
                        selectedSuggestion.suggestions.find(
                          (item: any) => item.type === selectedFixType,
                        )?.title
                      }
                    </Text>
                  </p>
                  <p>
                    风险等级:{' '}
                    {renderRiskTag(
                      selectedSuggestion.suggestions.find(
                        (item: any) => item.type === selectedFixType,
                      )?.risk || 'medium',
                    )}
                  </p>
                </>
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {selectedSuggestion.suggestions.find(
              (item: any) => item.type === selectedFixType,
            )?.risk === 'high' && (
              <Alert
                message="高风险操作警告"
                description="此操作为高风险操作，可能影响订单数据完整性。请确认已经充分了解操作后果。"
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Form.Item
              name="remark"
              label="操作备注"
              rules={[{ required: true, message: '请输入操作备注' }]}
            >
              <TextArea rows={3} placeholder="请输入操作备注，说明修复原因" />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" loading={applyLoading}>
                  确认应用
                </Button>
                <Button onClick={() => setConfirmModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default FixSuggestionsPanel;
