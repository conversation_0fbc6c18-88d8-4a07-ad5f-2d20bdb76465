import {
  getFixLogs,
} from '@/services/data-consistency';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Alert, Card, Col, Row, Tabs, Typography, message } from 'antd';
import React, { useEffect, useState } from 'react';
import AnomalyDetectionPanel from './AnomalyDetectionPanel';
import FixLogsPanel from './FixLogsPanel';
import './index.less';

const { Paragraph } = Typography;

/**
 * 订单金额异常检查主页面
 */
const DataConsistency: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [loading, setLoading] = useState(false);
  const [logsLoading, setLogsLoading] = useState(false);
  const [fixLogs, setFixLogs] = useState<API.FixLogsResult>();
  const [activeTab, setActiveTab] = useState('detection');

  // 加载修复日志
  const loadFixLogs = async (params?: any) => {
    setLogsLoading(true);
    try {
      const res = await getFixLogs(params);
      if (!res.errCode) {
        setFixLogs(res.data);
      } else {
        message.error(res.msg || '获取修复日志失败');
      }
    } catch (error) {
      console.error('加载修复日志失败:', error);
      message.error('获取修复日志失败');
    } finally {
      setLogsLoading(false);
    }
  };







  useEffect(() => {
    // 暂时注释掉自动加载，等后端接口实现后再启用
    // loadStatistics();
    // loadAnomalyRecords();
  }, []);

  const tabItems = [
    {
      key: 'detection',
      label: '异常检测与修复',
      children: (
        <AnomalyDetectionPanel
          loading={loading}
          currentUser={initialState}
        />
      ),
    },

    {
      key: 'logs',
      label: '修复日志',
      children: (
        <FixLogsPanel
          loading={logsLoading}
          data={fixLogs}
          onRefresh={loadFixLogs}
        />
      ),
    },
  ];

  return (
    <PageContainer
      title="数据一致性维护"
      className="order-amount-anomaly"
      content={
        <div>
          <Paragraph>
            订单金额异常检查系统提供一体化的检测与修复功能，专注于检测支付计算逻辑异常，确保"原价 - 权益卡优惠 - 代金券优惠 = 实付金额"的计算正确性，并提供智能修复建议。
          </Paragraph>
          <Alert
            message="操作建议"
            description="一体化操作流程：在异常检测与修复页面执行检测 → 查看修复建议 → 选择合适的修复方案 → 应用修复方案 → 查看修复日志验证效果"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>
      }
    >
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={tabItems}
            />
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default DataConsistency;
