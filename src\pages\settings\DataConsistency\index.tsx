import {
  checkOrderAmountAnomaly,
  generateAnomalyReport,
  getFixLogs,
} from '@/services/data-consistency';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Alert, Card, Col, Row, Tabs, Typography, message } from 'antd';
import React, { useEffect, useState } from 'react';
import AnomalyDetectionPanel from './AnomalyDetectionPanel';
import FixLogsPanel from './FixLogsPanel';
import FixSuggestionsPanel from './FixSuggestionsPanel';
import './index.less';

const { Paragraph } = Typography;

/**
 * 订单金额异常检查主页面
 */
const DataConsistency: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [loading, setLoading] = useState(false);
  const [logsLoading, setLogsLoading] = useState(false);
  const [fixLogs, setFixLogs] = useState<API.FixLogsResult>();
  const [activeTab, setActiveTab] = useState('detection');

  // 加载修复日志
  const loadFixLogs = async (params?: any) => {
    setLogsLoading(true);
    try {
      const res = await getFixLogs(params);
      if (!res.errCode) {
        setFixLogs(res.data);
      } else {
        message.error(res.msg || '获取修复日志失败');
      }
    } catch (error) {
      console.error('加载修复日志失败:', error);
      message.error('获取修复日志失败');
    } finally {
      setLogsLoading(false);
    }
  };

  // 检查订单金额异常并生成修复建议
  const handleCheckAnomaly = async (params?: any) => {
    setLoading(true);
    try {
      const res = await checkOrderAmountAnomaly(params);
      if (!res.errCode) {
        message.success(`检查完成，发现 ${res.data?.anomalyCount || 0} 个异常`);
        // 如果有异常，切换到修复建议标签页
        if (res.data?.anomalyCount && res.data.anomalyCount > 0) {
          setActiveTab('suggestions');
        }
      } else {
        message.error(res.msg || '异常检查失败');
      }
    } catch (error) {
      message.error('异常检查失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量检查（现在使用同一个接口）
  const handleBatchCheck = async (params?: any) => {
    // 直接调用检查异常函数，因为接口已经合并
    await handleCheckAnomaly(params);
  };

  // 生成异常报告
  const handleGenerateReport = async (params?: any) => {
    setLoading(true);
    try {
      const res = await generateAnomalyReport(params);
      if (!res.errCode) {
        message.success('报告生成成功');
        // 这里可以处理报告下载或显示
        console.log('异常报告:', res.data);
      } else {
        message.error(res.msg || '报告生成失败');
      }
    } catch (error) {
      message.error('报告生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成修复建议（使用合并后的检查接口）
  const handleGenerateFixSuggestions = async (params?: any) => {
    setLoading(true);
    try {
      const res = await checkOrderAmountAnomaly(params);
      if (!res.errCode) {
        message.success(
          `生成修复建议成功，发现 ${res.data?.anomalyCount || 0} 个异常`,
        );
        // 切换到修复建议标签页
        setActiveTab('suggestions');
      } else {
        message.error(res.msg || '生成修复建议失败');
      }
    } catch (error) {
      message.error('生成修复建议失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 暂时注释掉自动加载，等后端接口实现后再启用
    // loadStatistics();
    // loadAnomalyRecords();
  }, []);

  const tabItems = [
    {
      key: 'detection',
      label: '异常检测',
      children: (
        <AnomalyDetectionPanel
          loading={loading}
          onCheckAnomaly={handleCheckAnomaly}
          onBatchCheck={handleBatchCheck}
          onGenerateReport={handleGenerateReport}
          onGenerateFixSuggestions={handleGenerateFixSuggestions}
        />
      ),
    },
    {
      key: 'suggestions',
      label: '修复建议',
      children: (
        <FixSuggestionsPanel
          loading={loading}
          onRefresh={() => {
            // 修复完成后可以刷新其他数据
          }}
          currentUser={initialState}
        />
      ),
    },

    {
      key: 'logs',
      label: '修复日志',
      children: (
        <FixLogsPanel
          loading={logsLoading}
          data={fixLogs}
          onRefresh={loadFixLogs}
        />
      ),
    },
  ];

  return (
    <PageContainer
      title="数据一致性维护"
      className="order-amount-anomaly"
      content={
        <div>
          <Paragraph>
            订单金额异常检查系统专注于检测支付计算逻辑异常，确保"原价 -
            权益卡优惠 - 代金券优惠 =
            实付金额"的计算正确性，服务于收入统计数据验证。
          </Paragraph>
          <Alert
            message="操作建议"
            description="简化的操作流程：执行异常检测 → 查看修复建议 → 选择合适的修复方案 → 应用修复方案 → 查看修复日志验证效果"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>
      }
    >
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={tabItems}
            />
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default DataConsistency;
