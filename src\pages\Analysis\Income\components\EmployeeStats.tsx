import * as revenueStatistics from '@/services/revenue-statistics';
import { formatAmount } from '@/utils/format';
import { ProTable } from '@ant-design/pro-components';
import { Avatar, message } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useRef } from 'react';

interface EmployeeStatsProps {
  dateRange: [Dayjs, Dayjs];
}

const EmployeeStats: React.FC<EmployeeStatsProps> = ({ dateRange }) => {
  const actionRef = useRef<any>();

  const columns = [
    {
      title: '员工信息',
      dataIndex: 'employee',
      key: 'employee',
      width: 200,
      render: (employee: any) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Avatar src={employee?.avatar} size="small">
            {employee?.name?.charAt(0)}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500 }}>{employee?.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {employee?.phone}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '总订单数',
      dataIndex: 'totalOrderCount',
      key: 'totalOrderCount',
      width: 100,
      render: (value: number) => `${value}单`,
      sorter: true,
    },
    {
      title: '有效收入',
      dataIndex: 'totalRevenue',
      key: 'totalRevenue',
      width: 120,
      render: (value: number) => `¥${formatAmount(value)}`,
      sorter: true,
    },
    {
      title: '主订单收入',
      dataIndex: ['mainOrder', 'effectiveRevenue'],
      key: 'mainOrderRevenue',
      width: 120,
      render: (value: number) => `¥${formatAmount(value)}`,
    },
    {
      title: '主订单退款',
      dataIndex: ['mainOrder', 'refundedAmount'],
      key: 'mainOrderRefund',
      width: 120,
      render: (value: number) => `¥${formatAmount(value)}`,
    },
    {
      title: '追加服务收入',
      dataIndex: ['additionalService', 'effectiveRevenue'],
      key: 'additionalServiceRevenue',
      width: 130,
      render: (value: number) => `¥${formatAmount(value)}`,
    },
    {
      title: '追加服务退款',
      dataIndex: ['additionalService', 'refundedAmount'],
      key: 'additionalServiceRefund',
      width: 130,
      render: (value: number) => `¥${formatAmount(value)}`,
    },
    {
      title: '平均收入',
      dataIndex: 'avgRevenue',
      key: 'avgRevenue',
      width: 120,
      render: (value: string | number) => `¥${formatAmount(value)}`,
      sorter: true,
    },
    {
      title: '评分',
      dataIndex: ['employee', 'rating'],
      key: 'rating',
      width: 80,
      render: (value: number) => formatAmount(value, 1) || '-',
    },
  ];

  const fetchData = async (params: any, sort: any) => {
    try {
      const sortBy = sort && Object.keys(sort)[0];
      const sortOrder = sort && sort[sortBy] === 'ascend' ? 'asc' : 'desc';

      const { errCode, msg, data } = await revenueStatistics.employeeStats({
        startDate: dateRange[0]?.format('YYYY-MM-DD'),
        endDate: dateRange[1]?.format('YYYY-MM-DD'),
        page: params.current,
        pageSize: params.pageSize,
        sortBy: sortBy || 'totalRevenue',
        sortOrder: sortOrder || 'desc',
      });

      if (errCode) {
        message.error(msg || '获取员工收入统计失败');
        return {
          data: [],
          success: false,
          total: 0,
        };
      }

      return {
        data: data?.list || [],
        success: true,
        total: data?.total || 0,
      };
    } catch (error) {
      console.error('获取员工收入统计失败:', error);
      message.error('获取员工收入统计失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  return (
    <ProTable
      actionRef={actionRef}
      columns={columns}
      request={fetchData}
      rowKey="employeeId"
      pagination={{
        defaultPageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      search={false}
      dateFormatter="string"
      headerTitle="员工收入统计"
      toolBarRender={false}
      size="small"
      scroll={{ x: 'max-content' }}
      params={{
        dateRange,
      }}
    />
  );
};

export default EmployeeStats;
