import * as revenueStatistics from '@/services/revenue-statistics';
import { formatAmount } from '@/utils/format';
import { ProTable } from '@ant-design/pro-components';
import { message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useRef } from 'react';

interface ServiceStatsProps {
  dateRange: [Dayjs, Dayjs];
}

const ServiceStats: React.FC<ServiceStatsProps> = ({ dateRange }) => {
  const actionRef = useRef<any>();

  const columns = [
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
      width: 120,
    },
    {
      title: '有效订单数',
      dataIndex: 'effectiveOrderCount',
      key: 'effectiveOrderCount',
      width: 100,
      render: (value: number) => `${value}单`,
      sorter: true,
    },
    {
      title: '退款订单数',
      dataIndex: 'refundedOrderCount',
      key: 'refundedOrderCount',
      width: 100,
      render: (value: number) => `${value}单`,
      sorter: true,
    },
    {
      title: '总原价',
      dataIndex: 'totalOriginalPrice',
      key: 'totalOriginalPrice',
      width: 120,
      render: (value: number) => `¥${formatAmount(value)}`,
      sorter: true,
    },
    {
      title: '有效收入',
      dataIndex: 'effectiveRevenue',
      key: 'effectiveRevenue',
      width: 120,
      render: (value: number) => `¥${formatAmount(value)}`,
      sorter: true,
    },
    {
      title: '退款金额',
      dataIndex: 'refundedAmount',
      key: 'refundedAmount',
      width: 120,
      render: (value: number) => `¥${formatAmount(value)}`,
      sorter: true,
    },
    {
      title: '净收入',
      dataIndex: 'netRevenue',
      key: 'netRevenue',
      width: 120,
      render: (value: number) => `¥${formatAmount(value)}`,
      sorter: true,
    },
    {
      title: '平均收入',
      dataIndex: 'avgRevenue',
      key: 'avgRevenue',
      width: 120,
      render: (value: number) => `¥${formatAmount(value)}`,
      sorter: true,
    },
  ];

  const fetchData = async (params: any, sort: any) => {
    try {
      const sortBy = sort && Object.keys(sort)[0];
      const sortOrder = sort && sort[sortBy] === 'ascend' ? 'asc' : 'desc';

      const { errCode, msg, data } = await revenueStatistics.serviceStats({
        startDate: dateRange[0]?.format('YYYY-MM-DD'),
        endDate: dateRange[1]?.format('YYYY-MM-DD'),
        page: params.current,
        pageSize: params.pageSize,
        sortBy: sortBy || 'totalRevenue',
        sortOrder: sortOrder || 'desc',
      });

      if (errCode) {
        message.error(msg || '获取服务收入统计失败');
        return {
          data: [],
          success: false,
          total: 0,
        };
      }

      return {
        data: data?.list || [],
        success: true,
        total: data?.total || 0,
      };
    } catch (error) {
      console.error('获取服务收入统计失败:', error);
      message.error('获取服务收入统计失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  return (
    <ProTable
      actionRef={actionRef}
      columns={columns}
      request={fetchData}
      rowKey="serviceId"
      pagination={{
        defaultPageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      search={false}
      dateFormatter="string"
      headerTitle="服务收入统计"
      toolBarRender={false}
      size="small"
      scroll={{ x: 'max-content' }}
      params={{
        dateRange,
      }}
    />
  );
};

export default ServiceStats;
