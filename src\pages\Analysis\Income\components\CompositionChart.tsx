import * as revenueStatistics from '@/services/revenue-statistics';
import { formatNumber } from '@/utils/format';
import { Pie } from '@ant-design/plots';
import { ProCard } from '@ant-design/pro-components';
import { Col, Row, message } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface CompositionChartProps {
  dateRange: [Dayjs, Dayjs];
}

const CompositionChart: React.FC<CompositionChartProps> = ({ dateRange }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.RevenueCompositionStats>();

  const fetchData = async () => {
    if (!dateRange || !dateRange[0] || !dateRange[1]) return;

    setLoading(true);
    try {
      const { errCode, msg, data: result } = await revenueStatistics.composition({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取收入构成数据失败');
        return;
      }

      setData(result);
    } catch (error) {
      console.error('获取收入构成数据失败:', error);
      message.error('获取收入构成数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange]);

  // 收入构成数据
  const getRevenueData = () => {
    if (!data) return [];
    return [
      {
        type: '主订单收入',
        value: data.revenueComposition?.mainOrderRevenue || 0,
        percentage: parseFloat(data.revenueComposition?.mainOrderPercentage || '0'),
      },
      {
        type: '追加服务收入',
        value: data.revenueComposition?.additionalServiceRevenue || 0,
        percentage: parseFloat(data.revenueComposition?.additionalServicePercentage || '0'),
      },
    ];
  };

  // 订单构成数据
  const getOrderData = () => {
    if (!data) return [];
    return [
      {
        type: '主订单数量',
        value: data.orderComposition?.mainOrderCount || 0,
        percentage: parseFloat(data.orderComposition?.mainOrderPercentage || '0'),
      },
      {
        type: '追加服务数量',
        value: data.orderComposition?.additionalServiceCount || 0,
        percentage: parseFloat(data.orderComposition?.additionalServicePercentage || '0'),
      },
    ];
  };

  // 优惠构成数据
  const getDiscountData = () => {
    if (!data) return [];
    const mainOrderDiscount = data.discountComposition?.mainOrderDiscount || 0;
    const additionalServiceDiscount = data.discountComposition?.additionalServiceDiscount || 0;
    const total = mainOrderDiscount + additionalServiceDiscount;

    return [
      {
        type: '主订单优惠',
        value: mainOrderDiscount,
        percentage: total > 0 ? (mainOrderDiscount / total) * 100 : 0,
      },
      {
        type: '追加服务优惠',
        value: additionalServiceDiscount,
        percentage: total > 0 ? (additionalServiceDiscount / total) * 100 : 0,
      },
    ];
  };

  // 通用饼图配置
  const createPieConfig = (data: any[], title: string, suffix: string) => ({
    data,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    height: 250,
    label: {
      text: (d: any) => {
        return `${d.type}\n${d.value}${suffix} (${formatNumber(d.percentage, 1)}%)`;
      },
      position: 'spider' as const,
    },
    legend: {
      color: {
        title: false,
        position: 'bottom' as const,
        rowPadding: 5,
      },
    },
    color: ['#1890ff', '#52c41a'],
    tooltip: {
      title: (d: any) => d.type,
      items: [
        {
          field: 'value',
          name: title,
          valueFormatter: (value: any) => `${value}${suffix}`,
        },
      ],
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  });

  return (
    <ProCard title="收入构成分析" loading={loading}>
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <ProCard title="收入构成" size="small">
            <Pie {...createPieConfig(getRevenueData(), '金额', '元')} />
          </ProCard>
        </Col>
        <Col xs={24} lg={8}>
          <ProCard title="订单构成" size="small">
            <Pie {...createPieConfig(getOrderData(), '数量', '单')} />
          </ProCard>
        </Col>
        <Col xs={24} lg={8}>
          <ProCard title="优惠构成" size="small">
            <Pie {...createPieConfig(getDiscountData(), '优惠金额', '元')} />
          </ProCard>
        </Col>
      </Row>
    </ProCard>
  );
};

export default CompositionChart;
